@echo off
echo Nettoyage force de ClaraVerse...

echo Arret des processus Electron...
taskkill /f /im electron.exe 2>nul
taskkill /f /im node.exe 2>nul

echo Attente de 5 secondes...
timeout /t 5 /nobreak >nul

echo Suppression forcee de node_modules...
if exist node_modules (
    rmdir /s /q node_modules 2>nul
    if exist node_modules (
        echo Tentative avec robocopy...
        mkdir empty_temp
        robocopy empty_temp node_modules /mir /r:0 /w:0 >nul 2>&1
        rmdir /s /q node_modules 2>nul
        rmdir /s /q empty_temp 2>nul
    )
)

echo Suppression des fichiers de verrouillage...
del package-lock.json 2>nul
del yarn.lock 2>nul
del pnpm-lock.yaml 2>nul

echo Nettoyage du cache npm...
npm cache clean --force

echo Nettoyage termine!
pause
