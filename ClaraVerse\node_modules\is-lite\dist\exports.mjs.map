{"version": 3, "sources": ["../src/helpers.ts", "../src/index.ts", "../src/exports.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/ban-types */\nimport type { ObjectTypes, Primitive, PrimitiveTypes } from './types';\n\nexport const objectTypes = [\n  'Array',\n  'ArrayBuffer',\n  'AsyncFunction',\n  'AsyncGenerator',\n  'AsyncGeneratorFunction',\n  'Date',\n  'Error',\n  'Function',\n  'Generator',\n  'GeneratorFunction',\n  'HTMLElement',\n  'Map',\n  'Object',\n  'Promise',\n  'RegExp',\n  'Set',\n  'WeakMap',\n  'WeakSet',\n] as const;\n\nexport const primitiveTypes = [\n  'bigint',\n  'boolean',\n  'null',\n  'number',\n  'string',\n  'symbol',\n  'undefined',\n] as const;\n\nexport function getObjectType(value: unknown): ObjectTypes | undefined {\n  const objectTypeName = Object.prototype.toString.call(value).slice(8, -1);\n\n  if (/HTML\\w+Element/.test(objectTypeName)) {\n    return 'HTMLElement';\n  }\n\n  if (isObjectType(objectTypeName)) {\n    return objectTypeName;\n  }\n\n  return undefined;\n}\n\nexport function isObjectOfType<T>(type: string) {\n  return (value: unknown): value is T => getObjectType(value) === type;\n}\n\nexport function isObjectType(name: unknown): name is ObjectTypes {\n  return objectTypes.includes(name as ObjectTypes);\n}\n\nexport function isOfType<T extends Primitive | Function>(type: string) {\n  // eslint-disable-next-line valid-typeof\n  return (value: unknown): value is T => typeof value === type;\n}\n\nexport function isPrimitiveType(name: unknown): name is PrimitiveTypes {\n  return primitiveTypes.includes(name as PrimitiveTypes);\n}\n", "/* eslint-disable @typescript-eslint/ban-types */\nimport { getObjectType, isObjectOfType, isOfType, isPrimitiveType } from './helpers';\nimport type { Class, PlainObject, Primitive, TypeName } from './types';\n\nconst DOM_PROPERTIES_TO_CHECK: Array<keyof HTMLElement> = [\n  'innerHTML',\n  'ownerDocument',\n  'style',\n  'attributes',\n  'nodeValue',\n];\n\nfunction is(value: unknown): TypeName {\n  if (value === null) {\n    return 'null';\n  }\n\n  switch (typeof value) {\n    case 'bigint':\n      return 'bigint';\n    case 'boolean':\n      return 'boolean';\n    case 'number':\n      return 'number';\n    case 'string':\n      return 'string';\n    case 'symbol':\n      return 'symbol';\n    case 'undefined':\n      return 'undefined';\n    default:\n  }\n\n  if (is.array(value)) {\n    return 'Array';\n  }\n\n  if (is.plainFunction(value)) {\n    return 'Function';\n  }\n\n  const tagType = getObjectType(value);\n\n  if (tagType) {\n    return tagType;\n  }\n  /* c8 ignore next 3 */\n\n  return 'Object';\n}\n\nis.array = Array.isArray;\n\nis.arrayOf = (target: unknown[], predicate: (v: unknown) => boolean): boolean => {\n  if (!is.array(target) && !is.function(predicate)) {\n    return false;\n  }\n\n  return target.every(d => predicate(d));\n};\n\nis.asyncGeneratorFunction = (value: unknown): value is (...arguments_: any[]) => Promise<unknown> =>\n  getObjectType(value) === 'AsyncGeneratorFunction';\n\nis.asyncFunction = isObjectOfType<Function>('AsyncFunction');\n\nis.bigint = isOfType<bigint>('bigint');\n\nis.boolean = (value: unknown): value is boolean => {\n  return value === true || value === false;\n};\n\nis.date = isObjectOfType<Date>('Date');\n\nis.defined = (value: unknown): boolean => !is.undefined(value);\n\nis.domElement = (value: unknown): value is HTMLElement => {\n  return (\n    is.object(value) &&\n    !is.plainObject(value) &&\n    (value as HTMLElement).nodeType === 1 &&\n    is.string((value as HTMLElement).nodeName) &&\n    DOM_PROPERTIES_TO_CHECK.every(property => property in (value as HTMLElement))\n  );\n};\n\nis.empty = (value: unknown): boolean => {\n  return (\n    (is.string(value) && value.length === 0) ||\n    (is.array(value) && value.length === 0) ||\n    (is.object(value) && !is.map(value) && !is.set(value) && Object.keys(value).length === 0) ||\n    (is.set(value) && value.size === 0) ||\n    (is.map(value) && value.size === 0)\n  );\n};\n\nis.error = isObjectOfType<Error>('Error');\n\nis.function = isOfType<Function>('function');\n\nis.generator = (value: unknown): value is Generator => {\n  return (\n    is.iterable(value) &&\n    is.function((value as IterableIterator<unknown>).next) &&\n    is.function((value as IterableIterator<unknown>).throw)\n  );\n};\n\nis.generatorFunction = isObjectOfType<GeneratorFunction>('GeneratorFunction');\n\nis.instanceOf = <T>(instance: unknown, class_: Class<T>): instance is T => {\n  if (!instance || !(class_ as Class<T>)) {\n    return false;\n  }\n\n  return Object.getPrototypeOf(instance) === class_.prototype;\n};\n\nis.iterable = (value: unknown): value is IterableIterator<unknown> => {\n  return (\n    !is.nullOrUndefined(value) && is.function((value as IterableIterator<unknown>)[Symbol.iterator])\n  );\n};\n\nis.map = isObjectOfType<Map<unknown, unknown>>('Map');\n\nis.nan = (value: unknown): boolean => {\n  return Number.isNaN(value as number);\n};\n\nis.null = (value: unknown): value is null => {\n  return value === null;\n};\n\nis.nullOrUndefined = (value: unknown): value is null | undefined => {\n  return is.null(value) || is.undefined(value);\n};\n\nis.number = (value: unknown): value is number => {\n  return isOfType<number>('number')(value) && !is.nan(value);\n};\n\nis.numericString = (value: unknown): value is string => {\n  return is.string(value) && (value as string).length > 0 && !Number.isNaN(Number(value));\n};\n\nis.object = (value: unknown): value is object => {\n  return !is.nullOrUndefined(value) && (is.function(value) || typeof value === 'object');\n};\n\nis.oneOf = (target: unknown[], value: any): boolean => {\n  if (!is.array(target)) {\n    return false;\n  }\n\n  // eslint-disable-next-line unicorn/prefer-includes\n  return target.indexOf(value) > -1;\n};\n\nis.plainFunction = isObjectOfType<Function>('Function');\n\nis.plainObject = (value: unknown): value is PlainObject => {\n  if (getObjectType(value) !== 'Object') {\n    return false;\n  }\n\n  const prototype = Object.getPrototypeOf(value);\n\n  return prototype === null || prototype === Object.getPrototypeOf({});\n};\n\nis.primitive = (value: unknown): value is Primitive =>\n  is.null(value) || isPrimitiveType(typeof value);\n\nis.promise = isObjectOfType<Promise<unknown>>('Promise');\n\nis.propertyOf = (\n  target: PlainObject,\n  key: string,\n  predicate?: (v: unknown) => boolean,\n): boolean => {\n  if (!is.object(target) || !key) {\n    return false;\n  }\n\n  const value = target[key];\n\n  if (is.function(predicate)) {\n    return predicate(value);\n  }\n\n  return is.defined(value);\n};\n\nis.regexp = isObjectOfType<RegExp>('RegExp');\n\nis.set = isObjectOfType<Set<PlainObject>>('Set');\n\nis.string = isOfType<string>('string');\n\nis.symbol = isOfType<symbol>('symbol');\n\nis.undefined = isOfType<undefined>('undefined');\n\nis.weakMap = isObjectOfType<WeakMap<PlainObject, unknown>>('WeakMap');\n\nis.weakSet = isObjectOfType<WeakSet<PlainObject>>('WeakSet');\n\nexport default is;\n", "import is from './index';\n\nexport const isArray = is.array;\nexport const isArrayOf = is.arrayOf;\nexport const isAsyncGeneratorFunction = is.asyncGeneratorFunction;\nexport const isAsyncFunction = is.asyncFunction;\nexport const isBigInt = is.bigint;\nexport const isBoolean = is.boolean;\nexport const isDate = is.date;\nexport const isDefined = is.defined;\nexport const isDomElement = is.domElement;\nexport const isEmpty = is.empty;\nexport const isError = is.error;\nexport const isFunction = is.function;\nexport const isGenerator = is.generator;\nexport const isGeneratorFunction = is.generatorFunction;\nexport const isInstanceOf = is.instanceOf;\nexport const isIterable = is.iterable;\nexport const isMap = is.map;\nexport const isNan = is.nan;\nexport const isNull = is.null;\nexport const isNullOrUndefined = is.nullOrUndefined;\nexport const isNumber = is.number;\nexport const isNumericString = is.numericString;\nexport const isObject = is.object;\nexport const isOneOf = is.oneOf;\nexport const isPlainFunction = is.plainFunction;\nexport const isPlainObject = is.plainObject;\nexport const isPrimitive = is.primitive;\nexport const isPromise = is.promise;\nexport const isPropertyOf = is.propertyOf;\nexport const isRegexp = is.regexp;\nexport const isSet = is.set;\nexport const isString = is.string;\nexport const isSymbol = is.symbol;\nexport const isUndefined = is.undefined;\nexport const isWeakMap = is.weakMap;\nexport const isWeakSet = is.weakSet;\n"], "mappings": ";AAGO,IAAM,cAAc;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEO,IAAM,iBAAiB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEO,SAAS,cAAc,OAAyC;AACrE,QAAM,iBAAiB,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AAExE,MAAI,iBAAiB,KAAK,cAAc,GAAG;AACzC,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,cAAc,GAAG;AAChC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEO,SAAS,eAAkB,MAAc;AAC9C,SAAO,CAAC,UAA+B,cAAc,KAAK,MAAM;AAClE;AAEO,SAAS,aAAa,MAAoC;AAC/D,SAAO,YAAY,SAAS,IAAmB;AACjD;AAEO,SAAS,SAAyC,MAAc;AAErE,SAAO,CAAC,UAA+B,OAAO,UAAU;AAC1D;AAEO,SAAS,gBAAgB,MAAuC;AACrE,SAAO,eAAe,SAAS,IAAsB;AACvD;;;AC3DA,IAAM,0BAAoD;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,GAAG,OAA0B;AACpC,MAAI,UAAU,MAAM;AAClB,WAAO;AAAA,EACT;AAEA,UAAQ,OAAO,OAAO;AAAA,IACpB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,GAAG,MAAM,KAAK,GAAG;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,GAAG,cAAc,KAAK,GAAG;AAC3B,WAAO;AAAA,EACT;AAEA,QAAM,UAAU,cAAc,KAAK;AAEnC,MAAI,SAAS;AACX,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AAEA,GAAG,QAAQ,MAAM;AAEjB,GAAG,UAAU,CAAC,QAAmB,cAAgD;AAC/E,MAAI,CAAC,GAAG,MAAM,MAAM,KAAK,CAAC,GAAG,SAAS,SAAS,GAAG;AAChD,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,MAAM,OAAK,UAAU,CAAC,CAAC;AACvC;AAEA,GAAG,yBAAyB,CAAC,UAC3B,cAAc,KAAK,MAAM;AAE3B,GAAG,gBAAgB,eAAyB,eAAe;AAE3D,GAAG,SAAS,SAAiB,QAAQ;AAErC,GAAG,UAAU,CAAC,UAAqC;AACjD,SAAO,UAAU,QAAQ,UAAU;AACrC;AAEA,GAAG,OAAO,eAAqB,MAAM;AAErC,GAAG,UAAU,CAAC,UAA4B,CAAC,GAAG,UAAU,KAAK;AAE7D,GAAG,aAAa,CAAC,UAAyC;AACxD,SACE,GAAG,OAAO,KAAK,KACf,CAAC,GAAG,YAAY,KAAK,KACpB,MAAsB,aAAa,KACpC,GAAG,OAAQ,MAAsB,QAAQ,KACzC,wBAAwB,MAAM,cAAY,YAAa,KAAqB;AAEhF;AAEA,GAAG,QAAQ,CAAC,UAA4B;AACtC,SACG,GAAG,OAAO,KAAK,KAAK,MAAM,WAAW,KACrC,GAAG,MAAM,KAAK,KAAK,MAAM,WAAW,KACpC,GAAG,OAAO,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,OAAO,KAAK,KAAK,EAAE,WAAW,KACtF,GAAG,IAAI,KAAK,KAAK,MAAM,SAAS,KAChC,GAAG,IAAI,KAAK,KAAK,MAAM,SAAS;AAErC;AAEA,GAAG,QAAQ,eAAsB,OAAO;AAExC,GAAG,WAAW,SAAmB,UAAU;AAE3C,GAAG,YAAY,CAAC,UAAuC;AACrD,SACE,GAAG,SAAS,KAAK,KACjB,GAAG,SAAU,MAAoC,IAAI,KACrD,GAAG,SAAU,MAAoC,KAAK;AAE1D;AAEA,GAAG,oBAAoB,eAAkC,mBAAmB;AAE5E,GAAG,aAAa,CAAI,UAAmB,WAAoC;AACzE,MAAI,CAAC,YAAY,CAAE,QAAqB;AACtC,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,eAAe,QAAQ,MAAM,OAAO;AACpD;AAEA,GAAG,WAAW,CAAC,UAAuD;AACpE,SACE,CAAC,GAAG,gBAAgB,KAAK,KAAK,GAAG,SAAU,MAAoC,OAAO,QAAQ,CAAC;AAEnG;AAEA,GAAG,MAAM,eAAsC,KAAK;AAEpD,GAAG,MAAM,CAAC,UAA4B;AACpC,SAAO,OAAO,MAAM,KAAe;AACrC;AAEA,GAAG,OAAO,CAAC,UAAkC;AAC3C,SAAO,UAAU;AACnB;AAEA,GAAG,kBAAkB,CAAC,UAA8C;AAClE,SAAO,GAAG,KAAK,KAAK,KAAK,GAAG,UAAU,KAAK;AAC7C;AAEA,GAAG,SAAS,CAAC,UAAoC;AAC/C,SAAO,SAAiB,QAAQ,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK;AAC3D;AAEA,GAAG,gBAAgB,CAAC,UAAoC;AACtD,SAAO,GAAG,OAAO,KAAK,KAAM,MAAiB,SAAS,KAAK,CAAC,OAAO,MAAM,OAAO,KAAK,CAAC;AACxF;AAEA,GAAG,SAAS,CAAC,UAAoC;AAC/C,SAAO,CAAC,GAAG,gBAAgB,KAAK,MAAM,GAAG,SAAS,KAAK,KAAK,OAAO,UAAU;AAC/E;AAEA,GAAG,QAAQ,CAAC,QAAmB,UAAwB;AACrD,MAAI,CAAC,GAAG,MAAM,MAAM,GAAG;AACrB,WAAO;AAAA,EACT;AAGA,SAAO,OAAO,QAAQ,KAAK,IAAI;AACjC;AAEA,GAAG,gBAAgB,eAAyB,UAAU;AAEtD,GAAG,cAAc,CAAC,UAAyC;AACzD,MAAI,cAAc,KAAK,MAAM,UAAU;AACrC,WAAO;AAAA,EACT;AAEA,QAAM,YAAY,OAAO,eAAe,KAAK;AAE7C,SAAO,cAAc,QAAQ,cAAc,OAAO,eAAe,CAAC,CAAC;AACrE;AAEA,GAAG,YAAY,CAAC,UACd,GAAG,KAAK,KAAK,KAAK,gBAAgB,OAAO,KAAK;AAEhD,GAAG,UAAU,eAAiC,SAAS;AAEvD,GAAG,aAAa,CACd,QACA,KACA,cACY;AACZ,MAAI,CAAC,GAAG,OAAO,MAAM,KAAK,CAAC,KAAK;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,OAAO,GAAG;AAExB,MAAI,GAAG,SAAS,SAAS,GAAG;AAC1B,WAAO,UAAU,KAAK;AAAA,EACxB;AAEA,SAAO,GAAG,QAAQ,KAAK;AACzB;AAEA,GAAG,SAAS,eAAuB,QAAQ;AAE3C,GAAG,MAAM,eAAiC,KAAK;AAE/C,GAAG,SAAS,SAAiB,QAAQ;AAErC,GAAG,SAAS,SAAiB,QAAQ;AAErC,GAAG,YAAY,SAAoB,WAAW;AAE9C,GAAG,UAAU,eAA8C,SAAS;AAEpE,GAAG,UAAU,eAAqC,SAAS;AAE3D,IAAO,cAAQ;;;AC9MR,IAAM,UAAU,YAAG;AACnB,IAAM,YAAY,YAAG;AACrB,IAAM,2BAA2B,YAAG;AACpC,IAAM,kBAAkB,YAAG;AAC3B,IAAM,WAAW,YAAG;AACpB,IAAM,YAAY,YAAG;AACrB,IAAM,SAAS,YAAG;AAClB,IAAM,YAAY,YAAG;AACrB,IAAM,eAAe,YAAG;AACxB,IAAM,UAAU,YAAG;AACnB,IAAM,UAAU,YAAG;AACnB,IAAM,aAAa,YAAG;AACtB,IAAM,cAAc,YAAG;AACvB,IAAM,sBAAsB,YAAG;AAC/B,IAAM,eAAe,YAAG;AACxB,IAAM,aAAa,YAAG;AACtB,IAAM,QAAQ,YAAG;AACjB,IAAM,QAAQ,YAAG;AACjB,IAAM,SAAS,YAAG;AAClB,IAAM,oBAAoB,YAAG;AAC7B,IAAM,WAAW,YAAG;AACpB,IAAM,kBAAkB,YAAG;AAC3B,IAAM,WAAW,YAAG;AACpB,IAAM,UAAU,YAAG;AACnB,IAAM,kBAAkB,YAAG;AAC3B,IAAM,gBAAgB,YAAG;AACzB,IAAM,cAAc,YAAG;AACvB,IAAM,YAAY,YAAG;AACrB,IAAM,eAAe,YAAG;AACxB,IAAM,WAAW,YAAG;AACpB,IAAM,QAAQ,YAAG;AACjB,IAAM,WAAW,YAAG;AACpB,IAAM,WAAW,YAAG;AACpB,IAAM,cAAc,YAAG;AACvB,IAAM,YAAY,YAAG;AACrB,IAAM,YAAY,YAAG;", "names": []}