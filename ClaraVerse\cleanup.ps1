# Script de nettoyage forcé pour ClaraVerse
Write-Host "Arrêt de tous les processus Electron..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -like "*electron*"} | Stop-Process -Force -ErrorAction SilentlyContinue

Write-Host "Attente de 3 secondes..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host "Suppression forcée de node_modules..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    cmd /c "rmdir /s /q node_modules 2>nul"
    Start-Sleep -Seconds 2
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
    }
}

Write-Host "Suppression des fichiers de verrouillage..." -ForegroundColor Yellow
Remove-Item "package-lock.json" -ErrorAction SilentlyContinue
Remove-Item "yarn.lock" -ErrorAction SilentlyContinue

Write-Host "Nettoyage du cache npm..." -ForegroundColor Yellow
npm cache clean --force

Write-Host "Nettoyage terminé!" -ForegroundColor Green
