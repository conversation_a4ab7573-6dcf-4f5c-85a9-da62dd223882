<!doctype html>
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-getting-started/installation" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.8.0">
<title data-rh="true">Installation | Clara Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:image" content="https://clara-docs.example.com/img/clara-social-card.jpg"><meta data-rh="true" name="twitter:image" content="https://clara-docs.example.com/img/clara-social-card.jpg"><meta data-rh="true" property="og:url" content="https://clara-docs.example.com/getting-started/installation"><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Installation | Clara Documentation"><meta data-rh="true" name="description" content="Install Clara on your system"><meta data-rh="true" property="og:description" content="Install Clara on your system"><link data-rh="true" rel="icon" href="/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://clara-docs.example.com/getting-started/installation"><link data-rh="true" rel="alternate" href="https://clara-docs.example.com/getting-started/installation" hreflang="en"><link data-rh="true" rel="alternate" href="https://clara-docs.example.com/getting-started/installation" hreflang="x-default"><script data-rh="true" type="application/ld+json">{"@context":"https://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"name":"Installation","item":"https://clara-docs.example.com/getting-started/installation"}]}</script><link rel="stylesheet" href="/assets/css/styles.d4e08c71.css">
<script src="/assets/js/runtime~main.63bcd324.js" defer="defer"></script>
<script src="/assets/js/main.a25a12dc.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<svg xmlns="http://www.w3.org/2000/svg" style="display: none;"><defs>
<symbol id="theme-svg-external-link" viewBox="0 0 24 24"><path fill="currentColor" d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"/></symbol>
</defs></svg>
<script>!function(){var t=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();document.documentElement.setAttribute("data-theme",t||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")),document.documentElement.setAttribute("data-theme-choice",t||"system")}(),function(){try{const c=new URLSearchParams(window.location.search).entries();for(var[t,e]of c)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><link rel="preload" as="image" href="/img/logo.svg"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="theme-layout-navbar navbar navbar--fixed-top"><div class="navbar__inner"><div class="theme-layout-navbar-left navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/logo.svg" alt="Clara Logo" class="themedComponent_mlkZ themedComponent--light_NVdE"><img src="/img/logo.svg" alt="Clara Logo" class="themedComponent_mlkZ themedComponent--dark_xIcU"></div><b class="navbar__title text--truncate">Clara</b></a><a aria-current="page" class="navbar__item navbar__link navbar__link--active" href="/">Documentation</a></div><div class="theme-layout-navbar-right navbar__items navbar__items--right"><a href="https://github.com/your-org/clara" target="_blank" rel="noopener noreferrer" class="navbar__item navbar__link">GitHub<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a><div class="toggle_vylO colorModeToggle_DEke"><button class="clean-btn toggleButton_gllP toggleButtonDisabled_aARS" type="button" disabled="" title="system mode" aria-label="Switch between dark and light mode (currently system mode)"><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP lightToggleIcon_pyhR"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP darkToggleIcon_wfgR"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" aria-hidden="true" class="toggleIcon_g3eP systemToggleIcon_QzmC"><path fill="currentColor" d="m12 21c4.971 0 9-4.029 9-9s-4.029-9-9-9-9 4.029-9 9 4.029 9 9 9zm4.95-13.95c1.313 1.313 2.05 3.093 2.05 4.95s-0.738 3.637-2.05 4.95c-1.313 1.313-3.093 2.05-4.95 2.05v-14c1.857 0 3.637 0.737 4.95 2.05z"></path></svg></button></div><div class="navbarSearchContainer_Bca1"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="theme-layout-main main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="/">Clara Documentation</a></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role="button" aria-expanded="true" href="/getting-started/introduction">Getting Started</a></div><ul class="menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/introduction">Introduction to Clara</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="/getting-started/installation">Installation</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="/getting-started/quick-start">Quick Start Guide</a></li></ul></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_VOVn"><div class="docItemContainer_Djhp"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">Getting Started</span></li><li class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link">Installation</span></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>Installation Guide</h1></header>
<p>Clara is available for Windows, macOS, and Linux. Choose the installation method that works best for you.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="system-requirements">System Requirements<a href="#system-requirements" class="hash-link" aria-label="Direct link to System Requirements" title="Direct link to System Requirements">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="minimum-requirements">Minimum Requirements<a href="#minimum-requirements" class="hash-link" aria-label="Direct link to Minimum Requirements" title="Direct link to Minimum Requirements">​</a></h3>
<ul>
<li><strong>OS</strong>: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)</li>
<li><strong>RAM</strong>: 4GB (8GB recommended)</li>
<li><strong>Storage</strong>: 2GB free space</li>
<li><strong>Node.js</strong>: Version 18+ (for development)</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="recommended-requirements">Recommended Requirements<a href="#recommended-requirements" class="hash-link" aria-label="Direct link to Recommended Requirements" title="Direct link to Recommended Requirements">​</a></h3>
<ul>
<li><strong>RAM</strong>: 16GB+ (for local AI models)</li>
<li><strong>GPU</strong>: NVIDIA GPU with 8GB+ VRAM (for image generation)</li>
<li><strong>Storage</strong>: 10GB+ free space (for models and data)</li>
</ul>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="download--install">Download &amp; Install<a href="#download--install" class="hash-link" aria-label="Direct link to Download &amp; Install" title="Direct link to Download &amp; Install">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="option-1-pre-built-releases-recommended">Option 1: Pre-built Releases (Recommended)<a href="#option-1-pre-built-releases-recommended" class="hash-link" aria-label="Direct link to Option 1: Pre-built Releases (Recommended)" title="Direct link to Option 1: Pre-built Releases (Recommended)">​</a></h3>
<p>Download the latest release for your platform:</p>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="windows">Windows<a href="#windows" class="hash-link" aria-label="Direct link to Windows" title="Direct link to Windows">​</a></h4>
<ol>
<li>Download <code>Clara-Setup-x.x.x.exe</code> from <a href="https://github.com/your-org/clara/releases" target="_blank" rel="noopener noreferrer">GitHub Releases</a></li>
<li>Run the installer as Administrator</li>
<li>Follow the installation wizard</li>
<li>Launch Clara from the Start Menu</li>
</ol>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="macos">macOS<a href="#macos" class="hash-link" aria-label="Direct link to macOS" title="Direct link to macOS">​</a></h4>
<ol>
<li>Download <code>Clara-x.x.x.dmg</code> from <a href="https://github.com/your-org/clara/releases" target="_blank" rel="noopener noreferrer">GitHub Releases</a></li>
<li>Open the DMG file</li>
<li>Drag Clara to your Applications folder</li>
<li>Launch Clara from Applications</li>
</ol>
<div class="theme-admonition theme-admonition-tip admonition_xJq3 alert alert--success"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 12 16"><path fill-rule="evenodd" d="M6.5 0C3.48 0 1 2.19 1 5c0 .92.55 2.25 1 3 1.34 2.25 1.78 2.78 2 4v1h5v-1c.22-1.22.66-1.75 2-4 .45-.75 1-2.08 1-3 0-2.81-2.48-5-5.5-5zm3.64 7.48c-.25.44-.47.8-.67 1.11-.86 1.41-1.25 2.06-1.45 3.23-.02.05-.02.11-.02.17H5c0-.06 0-.13-.02-.17-.2-1.17-.59-1.83-1.45-3.23-.2-.31-.42-.67-.67-1.11C2.44 6.78 2 5.65 2 5c0-2.2 2.02-4 4.5-4 1.22 0 2.36.42 3.22 1.19C10.55 2.94 11 3.94 11 5c0 .66-.44 1.78-.86 2.48zM4 14h5c-.23 1.14-1.3 2-2.5 2s-2.27-.86-2.5-2z"></path></svg></span>macOS Security</div><div class="admonitionContent_BuS1"><p>If you see a security warning, go to <strong>System Preferences &gt; Security &amp; Privacy</strong> and click &quot;Open Anyway&quot;</p></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="linux">Linux<a href="#linux" class="hash-link" aria-label="Direct link to Linux" title="Direct link to Linux">​</a></h4>
<ol>
<li>Download <code>Clara-x.x.x.AppImage</code> from <a href="https://github.com/your-org/clara/releases" target="_blank" rel="noopener noreferrer">GitHub Releases</a></li>
<li>Make it executable: <code>chmod +x Clara-x.x.x.AppImage</code></li>
<li>Run: <code>./Clara-x.x.x.AppImage</code></li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="option-2-package-managers">Option 2: Package Managers<a href="#option-2-package-managers" class="hash-link" aria-label="Direct link to Option 2: Package Managers" title="Direct link to Option 2: Package Managers">​</a></h3>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="windows-chocolatey">Windows (Chocolatey)<a href="#windows-chocolatey" class="hash-link" aria-label="Direct link to Windows (Chocolatey)" title="Direct link to Windows (Chocolatey)">​</a></h4>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">choco </span><span class="token function" style="color:#d73a49">install</span><span class="token plain"> clara</span><br></span></code></pre></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="macos-homebrew">macOS (Homebrew)<a href="#macos-homebrew" class="hash-link" aria-label="Direct link to macOS (Homebrew)" title="Direct link to macOS (Homebrew)">​</a></h4>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token plain">brew </span><span class="token function" style="color:#d73a49">install</span><span class="token plain"> </span><span class="token parameter variable" style="color:#36acaa">--cask</span><span class="token plain"> clara</span><br></span></code></pre></div></div>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="linux-snap">Linux (Snap)<a href="#linux-snap" class="hash-link" aria-label="Direct link to Linux (Snap)" title="Direct link to Linux (Snap)">​</a></h4>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token function" style="color:#d73a49">sudo</span><span class="token plain"> snap </span><span class="token function" style="color:#d73a49">install</span><span class="token plain"> clara</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="development-installation">Development Installation<a href="#development-installation" class="hash-link" aria-label="Direct link to Development Installation" title="Direct link to Development Installation">​</a></h2>
<p>For developers who want to build from source:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="prerequisites">Prerequisites<a href="#prerequisites" class="hash-link" aria-label="Direct link to Prerequisites" title="Direct link to Prerequisites">​</a></h3>
<ul>
<li><strong>Node.js</strong> 18+</li>
<li><strong>npm</strong> or <strong>yarn</strong></li>
<li><strong>Git</strong></li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="build-from-source">Build from Source<a href="#build-from-source" class="hash-link" aria-label="Direct link to Build from Source" title="Direct link to Build from Source">​</a></h3>
<div class="language-bash codeBlockContainer_Ckt0 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_QJqH"><pre tabindex="0" class="prism-code language-bash codeBlock_bY9V thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color:#393A34"><span class="token comment" style="color:#999988;font-style:italic"># Clone the repository</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">git</span><span class="token plain"> clone https://github.com/your-org/clara.git</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token builtin class-name">cd</span><span class="token plain"> clara</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Install dependencies</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">npm</span><span class="token plain"> </span><span class="token function" style="color:#d73a49">install</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Start development server</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">npm</span><span class="token plain"> run dev</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token comment" style="color:#999988;font-style:italic"># Or build for production</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">npm</span><span class="token plain"> run build</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token function" style="color:#d73a49">npm</span><span class="token plain"> run electron:build</span><br></span></code></pre></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="ai-provider-setup">AI Provider Setup<a href="#ai-provider-setup" class="hash-link" aria-label="Direct link to AI Provider Setup" title="Direct link to AI Provider Setup">​</a></h2>
<p>Clara requires at least one AI provider to function. Set up your preferred provider:</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="openai">OpenAI<a href="#openai" class="hash-link" aria-label="Direct link to OpenAI" title="Direct link to OpenAI">​</a></h3>
<ol>
<li>Get an API key from <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer">OpenAI</a></li>
<li>Add to Clara settings: <code>sk-your-openai-key-here</code></li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="anthropic">Anthropic<a href="#anthropic" class="hash-link" aria-label="Direct link to Anthropic" title="Direct link to Anthropic">​</a></h3>
<ol>
<li>Get an API key from <a href="https://console.anthropic.com/" target="_blank" rel="noopener noreferrer">Anthropic</a></li>
<li>Add to Clara settings: <code>sk-ant-your-anthropic-key-here</code></li>
</ol>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="ollama-local">Ollama (Local)<a href="#ollama-local" class="hash-link" aria-label="Direct link to Ollama (Local)" title="Direct link to Ollama (Local)">​</a></h3>
<ol>
<li>Install Ollama: <code>curl -fsSL https://ollama.ai/install.sh | sh</code></li>
<li>Pull a model: <code>ollama pull llama2</code></li>
<li>Clara will auto-detect local Ollama</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="verification">Verification<a href="#verification" class="hash-link" aria-label="Direct link to Verification" title="Direct link to Verification">​</a></h2>
<p>After installation, verify Clara is working:</p>
<ol>
<li><strong>Launch Clara</strong></li>
<li><strong>Complete onboarding</strong> - Enter your name and configure AI provider</li>
<li><strong>Test AI Assistant</strong> - Send a message: &quot;Hello Clara!&quot;</li>
<li><strong>Check features</strong> - Try image generation and agent studio</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="troubleshooting">Troubleshooting<a href="#troubleshooting" class="hash-link" aria-label="Direct link to Troubleshooting" title="Direct link to Troubleshooting">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="common-issues">Common Issues<a href="#common-issues" class="hash-link" aria-label="Direct link to Common Issues" title="Direct link to Common Issues">​</a></h3>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="installation-fails">Installation Fails<a href="#installation-fails" class="hash-link" aria-label="Direct link to Installation Fails" title="Direct link to Installation Fails">​</a></h4>
<ul>
<li><strong>Windows</strong>: Run installer as Administrator</li>
<li><strong>macOS</strong>: Check Security &amp; Privacy settings</li>
<li><strong>Linux</strong>: Ensure AppImage has execute permissions</li>
</ul>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="clara-wont-start">Clara Won&#x27;t Start<a href="#clara-wont-start" class="hash-link" aria-label="Direct link to Clara Won&#x27;t Start" title="Direct link to Clara Won&#x27;t Start">​</a></h4>
<ul>
<li>Check system requirements</li>
<li>Verify Node.js version (18+)</li>
<li>Clear application data and restart</li>
</ul>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="ai-provider-issues">AI Provider Issues<a href="#ai-provider-issues" class="hash-link" aria-label="Direct link to AI Provider Issues" title="Direct link to AI Provider Issues">​</a></h4>
<ul>
<li>Verify API keys are correct</li>
<li>Check internet connectivity</li>
<li>Ensure sufficient API credits</li>
</ul>
<h4 class="anchor anchorWithStickyNavbar_LWe7" id="performance-issues">Performance Issues<a href="#performance-issues" class="hash-link" aria-label="Direct link to Performance Issues" title="Direct link to Performance Issues">​</a></h4>
<ul>
<li>Close unnecessary applications</li>
<li>Increase system RAM</li>
<li>Use local models (Ollama) for better privacy</li>
</ul>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="getting-help">Getting Help<a href="#getting-help" class="hash-link" aria-label="Direct link to Getting Help" title="Direct link to Getting Help">​</a></h3>
<p>If you encounter issues:</p>
<ol>
<li><strong>Check logs</strong> - Look in Clara&#x27;s debug console</li>
<li><strong>Search issues</strong> - Check <a href="https://github.com/your-org/clara/issues" target="_blank" rel="noopener noreferrer">GitHub Issues</a></li>
<li><strong>Report bugs</strong> - Create a new issue with details</li>
<li><strong>Community</strong> - Join our community discussions</li>
</ol>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="next-steps">Next Steps<a href="#next-steps" class="hash-link" aria-label="Direct link to Next Steps" title="Direct link to Next Steps">​</a></h2>
<p>Once Clara is installed:</p>
<ol>
<li><strong><a href="/getting-started/quick-start">Quick Start Guide</a></strong> - Get up and running</li>
<li><strong><a href="/getting-started/introduction">Introduction</a></strong> - Learn about Clara&#x27;s features</li>
</ol>
<hr>
<p><strong>Ready to get started?</strong> Head to the <a href="/getting-started/quick-start">Quick Start Guide</a> to begin your Clara journey!</p></div></article><nav class="docusaurus-mt-lg pagination-nav" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="/getting-started/introduction"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">Introduction to Clara</div></a><a class="pagination-nav__link pagination-nav__link--next" href="/getting-started/quick-start"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">Quick Start Guide</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#system-requirements" class="table-of-contents__link toc-highlight">System Requirements</a><ul><li><a href="#minimum-requirements" class="table-of-contents__link toc-highlight">Minimum Requirements</a></li><li><a href="#recommended-requirements" class="table-of-contents__link toc-highlight">Recommended Requirements</a></li></ul></li><li><a href="#download--install" class="table-of-contents__link toc-highlight">Download &amp; Install</a><ul><li><a href="#option-1-pre-built-releases-recommended" class="table-of-contents__link toc-highlight">Option 1: Pre-built Releases (Recommended)</a></li><li><a href="#option-2-package-managers" class="table-of-contents__link toc-highlight">Option 2: Package Managers</a></li></ul></li><li><a href="#development-installation" class="table-of-contents__link toc-highlight">Development Installation</a><ul><li><a href="#prerequisites" class="table-of-contents__link toc-highlight">Prerequisites</a></li><li><a href="#build-from-source" class="table-of-contents__link toc-highlight">Build from Source</a></li></ul></li><li><a href="#ai-provider-setup" class="table-of-contents__link toc-highlight">AI Provider Setup</a><ul><li><a href="#openai" class="table-of-contents__link toc-highlight">OpenAI</a></li><li><a href="#anthropic" class="table-of-contents__link toc-highlight">Anthropic</a></li><li><a href="#ollama-local" class="table-of-contents__link toc-highlight">Ollama (Local)</a></li></ul></li><li><a href="#verification" class="table-of-contents__link toc-highlight">Verification</a></li><li><a href="#troubleshooting" class="table-of-contents__link toc-highlight">Troubleshooting</a><ul><li><a href="#common-issues" class="table-of-contents__link toc-highlight">Common Issues</a></li><li><a href="#getting-help" class="table-of-contents__link toc-highlight">Getting Help</a></li></ul></li><li><a href="#next-steps" class="table-of-contents__link toc-highlight">Next Steps</a></li></ul></div></div></div></div></main></div></div></div><footer class="theme-layout-footer footer footer--dark"><div class="container container-fluid"><div class="row footer__links"><div class="theme-layout-footer-column col footer__col"><div class="footer__title">Documentation</div><ul class="footer__items clean-list"><li class="footer__item"><a class="footer__link-item" href="/getting-started/introduction">Getting Started</a></li><li class="footer__item"><a class="footer__link-item" href="/getting-started/installation">Installation</a></li><li class="footer__item"><a class="footer__link-item" href="/getting-started/quick-start">Quick Start</a></li></ul></div><div class="theme-layout-footer-column col footer__col"><div class="footer__title">Clara</div><ul class="footer__items clean-list"><li class="footer__item"><a href="https://github.com/your-org/clara" target="_blank" rel="noopener noreferrer" class="footer__link-item">GitHub<svg width="13.5" height="13.5" aria-hidden="true" class="iconExternalLink_nPIU"><use href="#theme-svg-external-link"></use></svg></a></li></ul></div></div><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 Clara. Built with ❤️ and Docusaurus.</div></div></div></footer></div>
</body>
</html>