import equal from '@gilbarbara/deep-equal';
import is from 'is-lite';
import { compareNumbers, compareValues, getIterables, includesOrEqualsTo, nested } from './helpers';
export default function treeChanges(previousData, data) {
    if ([previousData, data].some(is.nullOrUndefined)) {
        throw new Error('Missing required parameters');
    }
    if (![previousData, data].every(function (d) { return is.plainObject(d) || is.array(d); })) {
        throw new Error('Expected plain objects or array');
    }
    var added = function (key, value) {
        try {
            return compareValues(previousData, data, { key: key, type: 'added', value: value });
        }
        catch (_a) {
            /* istanbul ignore next */
            return false;
        }
    };
    var changed = function (key, actual, previous) {
        try {
            var left = nested(previousData, key);
            var right = nested(data, key);
            var hasActual = is.defined(actual);
            var hasPrevious = is.defined(previous);
            if (hasActual || hasPrevious) {
                var leftComparator = hasPrevious
                    ? includesOrEqualsTo(previous, left)
                    : !includesOrEqualsTo(actual, left);
                var rightComparator = includesOrEqualsTo(actual, right);
                return leftComparator && rightComparator;
            }
            if ([left, right].every(is.array) || [left, right].every(is.plainObject)) {
                return !equal(left, right);
            }
            return left !== right;
        }
        catch (_a) {
            /* istanbul ignore next */
            return false;
        }
    };
    var changedFrom = function (key, previous, actual) {
        if (!is.defined(key)) {
            return false;
        }
        try {
            var left = nested(previousData, key);
            var right = nested(data, key);
            var hasActual = is.defined(actual);
            return (includesOrEqualsTo(previous, left) &&
                (hasActual ? includesOrEqualsTo(actual, right) : !hasActual));
        }
        catch (_a) {
            /* istanbul ignore next */
            return false;
        }
    };
    /**
     * @deprecated
     * Use "changed" instead
     */
    var changedTo = function (key, actual) {
        if (!is.defined(key)) {
            return false;
        }
        /* istanbul ignore next */
        if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.warn('`changedTo` is deprecated! Replace it with `change`');
        }
        return changed(key, actual);
    };
    var decreased = function (key, actual, previous) {
        if (!is.defined(key)) {
            return false;
        }
        try {
            return compareNumbers(previousData, data, { key: key, actual: actual, previous: previous, type: 'decreased' });
        }
        catch (_a) {
            /* istanbul ignore next */
            return false;
        }
    };
    var emptied = function (key) {
        try {
            var _a = getIterables(previousData, data, { key: key }), left = _a[0], right = _a[1];
            return !!left.length && !right.length;
        }
        catch (_b) {
            /* istanbul ignore next */
            return false;
        }
    };
    var filled = function (key) {
        try {
            var _a = getIterables(previousData, data, { key: key }), left = _a[0], right = _a[1];
            return !left.length && !!right.length;
        }
        catch (_b) {
            /* istanbul ignore next */
            return false;
        }
    };
    var increased = function (key, actual, previous) {
        if (!is.defined(key)) {
            return false;
        }
        try {
            return compareNumbers(previousData, data, { key: key, actual: actual, previous: previous, type: 'increased' });
        }
        catch (_a) {
            /* istanbul ignore next */
            return false;
        }
    };
    var removed = function (key, value) {
        try {
            return compareValues(previousData, data, { key: key, type: 'removed', value: value });
        }
        catch (_a) {
            /* istanbul ignore next */
            return false;
        }
    };
    return { added: added, changed: changed, changedFrom: changedFrom, changedTo: changedTo, decreased: decreased, emptied: emptied, filled: filled, increased: increased, removed: removed };
}
export * from './types';
//# sourceMappingURL=index.js.map