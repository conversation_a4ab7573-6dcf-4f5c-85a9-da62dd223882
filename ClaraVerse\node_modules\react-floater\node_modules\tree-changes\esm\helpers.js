import equal from '@gilbarbara/deep-equal';
import is from 'is-lite';
export function canHaveLength() {
    var arguments_ = [];
    for (var _i = 0; _i < arguments.length; _i++) {
        arguments_[_i] = arguments[_i];
    }
    return arguments_.every(function (d) { return is.string(d) || is.array(d) || is.plainObject(d); });
}
export function checkEquality(left, right, value) {
    if (!isSameType(left, right)) {
        return false;
    }
    if ([left, right].every(is.array)) {
        return !left.some(hasValue(value)) && right.some(hasValue(value));
    }
    /* istanbul ignore else */
    if ([left, right].every(is.plainObject)) {
        return (!Object.entries(left).some(hasEntry(value)) && Object.entries(right).some(hasEntry(value)));
    }
    return right === value;
}
export function compareNumbers(previousData, data, options) {
    var actual = options.actual, key = options.key, previous = options.previous, type = options.type;
    var left = nested(previousData, key);
    var right = nested(data, key);
    var changed = [left, right].every(is.number) && (type === 'increased' ? left < right : left > right);
    if (!is.undefined(actual)) {
        changed = changed && right === actual;
    }
    if (!is.undefined(previous)) {
        changed = changed && left === previous;
    }
    return changed;
}
export function compareValues(previousData, data, options) {
    var key = options.key, type = options.type, value = options.value;
    var left = nested(previousData, key);
    var right = nested(data, key);
    var primary = type === 'added' ? left : right;
    var secondary = type === 'added' ? right : left;
    // console.log({ primary, secondary });
    if (!is.nullOrUndefined(value)) {
        if (is.defined(primary)) {
            // check if nested data matches
            if (is.array(primary) || is.plainObject(primary)) {
                return checkEquality(primary, secondary, value);
            }
        }
        else {
            return equal(secondary, value);
        }
        return false;
    }
    if ([left, right].every(is.array)) {
        return !secondary.every(isEqualPredicate(primary));
    }
    if ([left, right].every(is.plainObject)) {
        return hasExtraKeys(Object.keys(primary), Object.keys(secondary));
    }
    return (![left, right].every(function (d) { return is.primitive(d) && is.defined(d); }) &&
        (type === 'added'
            ? !is.defined(left) && is.defined(right)
            : is.defined(left) && !is.defined(right)));
}
export function getIterables(previousData, data, _a) {
    var _b = _a === void 0 ? {} : _a, key = _b.key;
    var left = nested(previousData, key);
    var right = nested(data, key);
    if (!isSameType(left, right)) {
        throw new TypeError('Inputs have different types');
    }
    if (!canHaveLength(left, right)) {
        throw new TypeError("Inputs don't have length");
    }
    if ([left, right].every(is.plainObject)) {
        left = Object.keys(left);
        right = Object.keys(right);
    }
    return [left, right];
}
export function hasEntry(input) {
    return function (_a) {
        var key = _a[0], value = _a[1];
        if (is.array(input)) {
            return (equal(input, value) ||
                input.some(function (d) { return equal(d, value) || (is.array(value) && isEqualPredicate(value)(d)); }));
        }
        /* istanbul ignore else */
        if (is.plainObject(input) && input[key]) {
            return !!input[key] && equal(input[key], value);
        }
        return equal(input, value);
    };
}
export function hasExtraKeys(left, right) {
    return right.some(function (d) { return !left.includes(d); });
}
export function hasValue(input) {
    return function (value) {
        if (is.array(input)) {
            return input.some(function (d) { return equal(d, value) || (is.array(value) && isEqualPredicate(value)(d)); });
        }
        return equal(input, value);
    };
}
export function includesOrEqualsTo(previousValue, value) {
    return is.array(previousValue)
        ? previousValue.some(function (d) { return equal(d, value); })
        : equal(previousValue, value);
}
export function isEqualPredicate(data) {
    return function (value) { return data.some(function (d) { return equal(d, value); }); };
}
export function isSameType() {
    var arguments_ = [];
    for (var _i = 0; _i < arguments.length; _i++) {
        arguments_[_i] = arguments[_i];
    }
    return (arguments_.every(is.array) ||
        arguments_.every(is.number) ||
        arguments_.every(is.plainObject) ||
        arguments_.every(is.string));
}
export function nested(data, property) {
    /* istanbul ignore else */
    if (is.plainObject(data) || is.array(data)) {
        /* istanbul ignore else */
        if (is.string(property)) {
            var props = property.split('.');
            return props.reduce(function (acc, d) { return acc && acc[d]; }, data);
        }
        /* istanbul ignore else */
        if (is.number(property)) {
            return data[property];
        }
        return data;
    }
    return data;
}
//# sourceMappingURL=helpers.js.map