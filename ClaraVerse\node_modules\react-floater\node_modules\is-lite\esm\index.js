var DOM_PROPERTIES_TO_CHECK = [
    'innerHTML',
    'ownerDocument',
    'style',
    'attributes',
    'nodeValue',
];
var objectTypes = [
    'Array',
    'ArrayBuffer',
    'AsyncFunction',
    'AsyncGenerator',
    'AsyncGeneratorFunction',
    'Date',
    'Error',
    'Function',
    'Generator',
    'GeneratorFunction',
    'HTMLElement',
    'Map',
    'Object',
    'Promise',
    'RegExp',
    'Set',
    'WeakMap',
    'WeakSet',
];
var primitiveTypes = [
    'bigint',
    'boolean',
    'null',
    'number',
    'string',
    'symbol',
    'undefined',
];
export function getObjectType(value) {
    var objectTypeName = Object.prototype.toString.call(value).slice(8, -1);
    if (/HTML\w+Element/.test(objectTypeName)) {
        return 'HTMLElement';
    }
    if (isObjectType(objectTypeName)) {
        return objectTypeName;
    }
    return undefined;
}
function isObjectOfType(type) {
    return function (value) { return getObjectType(value) === type; };
}
function isObjectType(name) {
    return objectTypes.includes(name);
}
// eslint-disable-next-line @typescript-eslint/ban-types
function isOfType(type) {
    return function (value) { return typeof value === type; };
}
function isPrimitiveType(name) {
    return primitiveTypes.includes(name);
}
function is(value) {
    if (value === null) {
        return 'null';
    }
    switch (typeof value) {
        case 'bigint':
            return 'bigint';
        case 'boolean':
            return 'boolean';
        case 'number':
            return 'number';
        case 'string':
            return 'string';
        case 'symbol':
            return 'symbol';
        case 'undefined':
            return 'undefined';
        default:
    }
    if (is.array(value)) {
        return 'Array';
    }
    if (is.plainFunction(value)) {
        return 'Function';
    }
    var tagType = getObjectType(value);
    /* istanbul ignore else */
    if (tagType) {
        return tagType;
    }
    /* istanbul ignore next */
    return 'Object';
}
is.array = Array.isArray;
is.arrayOf = function (target, predicate) {
    if (!is.array(target) && !is.function(predicate)) {
        return false;
    }
    return target.every(function (d) { return predicate(d); });
};
is.asyncGeneratorFunction = function (value) {
    return getObjectType(value) === 'AsyncGeneratorFunction';
};
// eslint-disable-next-line @typescript-eslint/ban-types
is.asyncFunction = isObjectOfType('AsyncFunction');
is.bigint = isOfType('bigint');
is.boolean = function (value) {
    return value === true || value === false;
};
is.date = isObjectOfType('Date');
is.defined = function (value) { return !is.undefined(value); };
is.domElement = function (value) {
    return (is.object(value) &&
        !is.plainObject(value) &&
        value.nodeType === 1 &&
        is.string(value.nodeName) &&
        DOM_PROPERTIES_TO_CHECK.every(function (property) { return property in value; }));
};
is.empty = function (value) {
    return ((is.string(value) && value.length === 0) ||
        (is.array(value) && value.length === 0) ||
        (is.object(value) && !is.map(value) && !is.set(value) && Object.keys(value).length === 0) ||
        (is.set(value) && value.size === 0) ||
        (is.map(value) && value.size === 0));
};
is.error = isObjectOfType('Error');
// eslint-disable-next-line @typescript-eslint/ban-types
is.function = isOfType('function');
is.generator = function (value) {
    return (is.iterable(value) &&
        is.function(value.next) &&
        is.function(value.throw));
};
is.generatorFunction = isObjectOfType('GeneratorFunction');
is.instanceOf = function (instance, class_) {
    if (!instance || !class_) {
        return false;
    }
    return Object.getPrototypeOf(instance) === class_.prototype;
};
is.iterable = function (value) {
    return (!is.nullOrUndefined(value) && is.function(value[Symbol.iterator]));
};
is.map = isObjectOfType('Map');
is.nan = function (value) {
    return Number.isNaN(value);
};
is.null = function (value) {
    return value === null;
};
is.nullOrUndefined = function (value) {
    return is.null(value) || is.undefined(value);
};
is.number = function (value) {
    return isOfType('number')(value) && !is.nan(value);
};
is.numericString = function (value) {
    return is.string(value) && value.length > 0 && !Number.isNaN(Number(value));
};
// eslint-disable-next-line @typescript-eslint/ban-types
is.object = function (value) {
    return !is.nullOrUndefined(value) && (is.function(value) || typeof value === 'object');
};
is.oneOf = function (target, value) {
    if (!is.array(target)) {
        return false;
    }
    // eslint-disable-next-line unicorn/prefer-includes
    return target.indexOf(value) > -1;
};
// eslint-disable-next-line @typescript-eslint/ban-types
is.plainFunction = isObjectOfType('Function');
is.plainObject = function (value) {
    if (getObjectType(value) !== 'Object') {
        return false;
    }
    var prototype = Object.getPrototypeOf(value);
    return prototype === null || prototype === Object.getPrototypeOf({});
};
is.primitive = function (value) {
    return is.null(value) || isPrimitiveType(typeof value);
};
is.promise = isObjectOfType('Promise');
is.propertyOf = function (target, key, predicate) {
    if (!is.object(target) || !key) {
        return false;
    }
    var value = target[key];
    if (is.function(predicate)) {
        return predicate(value);
    }
    return is.defined(value);
};
is.regexp = isObjectOfType('RegExp');
is.set = isObjectOfType('Set');
is.string = isOfType('string');
is.symbol = isOfType('symbol');
is.undefined = isOfType('undefined');
is.weakMap = isObjectOfType('WeakMap');
is.weakSet = isObjectOfType('WeakSet');
export * from './types';
export default is;
//# sourceMappingURL=index.js.map