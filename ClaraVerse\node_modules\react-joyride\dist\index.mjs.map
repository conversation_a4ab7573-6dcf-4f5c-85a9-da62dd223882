{"version": 3, "sources": ["../src/literals/index.ts", "../src/components/index.tsx", "../src/modules/dom.ts", "../src/modules/helpers.tsx", "../src/modules/step.ts", "../src/defaults.ts", "../src/styles.ts", "../src/modules/store.ts", "../src/components/Overlay.tsx", "../src/components/Spotlight.tsx", "../src/components/Portal.tsx", "../src/components/Step.tsx", "../src/modules/scope.ts", "../src/components/Beacon.tsx", "../src/components/Tooltip/index.tsx", "../src/components/Tooltip/Container.tsx", "../src/components/Tooltip/CloseButton.tsx"], "sourcesContent": ["export const ACTIONS = {\n  INIT: 'init',\n  START: 'start',\n  STOP: 'stop',\n  RESET: 'reset',\n  PREV: 'prev',\n  NEXT: 'next',\n  GO: 'go',\n  CLOSE: 'close',\n  SKIP: 'skip',\n  UPDATE: 'update',\n} as const;\n\nexport const EVENTS = {\n  TOUR_START: 'tour:start',\n  STEP_BEFORE: 'step:before',\n  BEACON: 'beacon',\n  TOOLTIP: 'tooltip',\n  STEP_AFTER: 'step:after',\n  TOUR_END: 'tour:end',\n  TOUR_STATUS: 'tour:status',\n  TARGET_NOT_FOUND: 'error:target_not_found',\n  ERROR: 'error',\n} as const;\n\nexport const LIFECYCLE = {\n  INIT: 'init',\n  READY: 'ready',\n  BEACON: 'beacon',\n  TOOLTIP: 'tooltip',\n  COMPLETE: 'complete',\n  ERROR: 'error',\n} as const;\n\nexport const ORIGIN = {\n  BUTTON_CLOSE: 'button_close',\n  BUTTON_PRIMARY: 'button_primary',\n  KEYBOARD: 'keyboard',\n  OVERLAY: 'overlay',\n} as const;\n\nexport const STATUS = {\n  IDLE: 'idle',\n  READY: 'ready',\n  WAITING: 'waiting',\n  RUNNING: 'running',\n  PAUSED: 'paused',\n  SKIPPED: 'skipped',\n  FINISHED: 'finished',\n  ERROR: 'error',\n} as const;\n", "import * as React from 'react';\nimport { ReactNode } from 'react';\nimport isEqual from '@gilbarbara/deep-equal';\nimport is from 'is-lite';\nimport treeChanges from 'tree-changes';\n\nimport {\n  canUseDOM,\n  getElement,\n  getScrollParent,\n  getScrollTo,\n  hasCustomScrollParent,\n  scrollTo,\n} from '~/modules/dom';\nimport { log, shouldScroll } from '~/modules/helpers';\nimport { getMergedStep, validateSteps } from '~/modules/step';\nimport createStore from '~/modules/store';\n\nimport { ACTIONS, EVENTS, LIFECYCLE, STATUS } from '~/literals';\n\nimport Overlay from '~/components/Overlay';\nimport Portal from '~/components/Portal';\n\nimport { defaultProps } from '~/defaults';\nimport { Actions, CallBackProps, Props, State, Status, StoreHelpers } from '~/types';\n\nimport Step from './Step';\n\nclass Joyride extends React.Component<Props, State> {\n  private readonly helpers: StoreHelpers;\n  private readonly store: ReturnType<typeof createStore>;\n\n  static defaultProps = defaultProps;\n\n  constructor(props: Props) {\n    super(props);\n\n    const { debug, getHelpers, run = true, stepIndex } = props;\n\n    this.store = createStore({\n      ...props,\n      controlled: run && is.number(stepIndex),\n    });\n    this.helpers = this.store.getHelpers();\n\n    const { addListener } = this.store;\n\n    log({\n      title: 'init',\n      data: [\n        { key: 'props', value: this.props },\n        { key: 'state', value: this.state },\n      ],\n      debug,\n    });\n\n    // Sync the store to this component's state.\n    addListener(this.syncState);\n\n    if (getHelpers) {\n      getHelpers(this.helpers);\n    }\n\n    this.state = this.store.getState();\n  }\n\n  componentDidMount() {\n    if (!canUseDOM()) {\n      return;\n    }\n\n    const { debug, disableCloseOnEsc, run, steps } = this.props;\n    const { start } = this.store;\n\n    if (validateSteps(steps, debug) && run) {\n      start();\n    }\n\n    if (!disableCloseOnEsc) {\n      document.body.addEventListener('keydown', this.handleKeyboard, { passive: true });\n    }\n  }\n\n  componentDidUpdate(previousProps: Props, previousState: State) {\n    if (!canUseDOM()) {\n      return;\n    }\n\n    const { action, controlled, index, status } = this.state;\n    const { debug, run, stepIndex, steps } = this.props;\n    const { stepIndex: previousStepIndex, steps: previousSteps } = previousProps;\n    const { reset, setSteps, start, stop, update } = this.store;\n    const { changed: changedProps } = treeChanges(previousProps, this.props);\n    const { changed, changedFrom } = treeChanges(previousState, this.state);\n    const step = getMergedStep(this.props, steps[index]);\n\n    const stepsChanged = !isEqual(previousSteps, steps);\n    const stepIndexChanged = is.number(stepIndex) && changedProps('stepIndex');\n    const target = getElement(step.target);\n\n    if (stepsChanged) {\n      if (validateSteps(steps, debug)) {\n        setSteps(steps);\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn('Steps are not valid', steps);\n      }\n    }\n\n    if (changedProps('run')) {\n      if (run) {\n        start(stepIndex);\n      } else {\n        stop();\n      }\n    }\n\n    if (stepIndexChanged) {\n      let nextAction: Actions =\n        is.number(previousStepIndex) && previousStepIndex < stepIndex ? ACTIONS.NEXT : ACTIONS.PREV;\n\n      if (action === ACTIONS.STOP) {\n        nextAction = ACTIONS.START;\n      }\n\n      if (!([STATUS.FINISHED, STATUS.SKIPPED] as Array<Status>).includes(status)) {\n        update({\n          action: action === ACTIONS.CLOSE ? ACTIONS.CLOSE : nextAction,\n          index: stepIndex,\n          lifecycle: LIFECYCLE.INIT,\n        });\n      }\n    }\n\n    // Update the index if the first step is not found\n    if (!controlled && status === STATUS.RUNNING && index === 0 && !target) {\n      this.store.update({ index: index + 1 });\n      this.callback({\n        ...this.state,\n        type: EVENTS.TARGET_NOT_FOUND,\n        step,\n      });\n    }\n\n    const callbackData = {\n      ...this.state,\n      index,\n      step,\n    };\n    const isAfterAction = changed('action', [\n      ACTIONS.NEXT,\n      ACTIONS.PREV,\n      ACTIONS.SKIP,\n      ACTIONS.CLOSE,\n    ]);\n\n    if (isAfterAction && changed('status', STATUS.PAUSED)) {\n      const previousStep = getMergedStep(this.props, steps[previousState.index]);\n\n      this.callback({\n        ...callbackData,\n        index: previousState.index,\n        lifecycle: LIFECYCLE.COMPLETE,\n        step: previousStep,\n        type: EVENTS.STEP_AFTER,\n      });\n    }\n\n    if (changed('status', [STATUS.FINISHED, STATUS.SKIPPED])) {\n      const previousStep = getMergedStep(this.props, steps[previousState.index]);\n\n      if (!controlled) {\n        this.callback({\n          ...callbackData,\n          index: previousState.index,\n          lifecycle: LIFECYCLE.COMPLETE,\n          step: previousStep,\n          type: EVENTS.STEP_AFTER,\n        });\n      }\n\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_END,\n        // Return the last step when the tour is finished\n        step: previousStep,\n        index: previousState.index,\n      });\n      reset();\n    } else if (changedFrom('status', [STATUS.IDLE, STATUS.READY], STATUS.RUNNING)) {\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_START,\n      });\n    } else if (changed('status') || changed('action', ACTIONS.RESET)) {\n      this.callback({\n        ...callbackData,\n        type: EVENTS.TOUR_STATUS,\n      });\n    }\n\n    this.scrollToStep(previousState);\n  }\n\n  componentWillUnmount() {\n    const { disableCloseOnEsc } = this.props;\n\n    if (!disableCloseOnEsc) {\n      document.body.removeEventListener('keydown', this.handleKeyboard);\n    }\n  }\n\n  /**\n   * Trigger the callback.\n   */\n  callback = (data: CallBackProps) => {\n    const { callback } = this.props;\n\n    if (is.function(callback)) {\n      callback(data);\n    }\n  };\n\n  /**\n   * Keydown event listener\n   */\n  handleKeyboard = (event: KeyboardEvent) => {\n    const { index, lifecycle } = this.state;\n    const { steps } = this.props;\n    const step = steps[index];\n\n    if (lifecycle === LIFECYCLE.TOOLTIP) {\n      if (event.code === 'Escape' && step && !step.disableCloseOnEsc) {\n        this.store.close('keyboard');\n      }\n    }\n  };\n\n  handleClickOverlay = () => {\n    const { index } = this.state;\n    const { steps } = this.props;\n\n    const step = getMergedStep(this.props, steps[index]);\n\n    if (!step.disableOverlayClose) {\n      this.helpers.close('overlay');\n    }\n  };\n\n  /**\n   * Sync the store with the component's state\n   */\n  syncState = (state: State) => {\n    this.setState(state);\n  };\n\n  scrollToStep(previousState: State) {\n    const { index, lifecycle, status } = this.state;\n    const {\n      debug,\n      disableScrollParentFix = false,\n      scrollDuration,\n      scrollOffset = 20,\n      scrollToFirstStep = false,\n      steps,\n    } = this.props;\n    const step = getMergedStep(this.props, steps[index]);\n\n    const target = getElement(step.target);\n    const shouldScrollToStep = shouldScroll({\n      isFirstStep: index === 0,\n      lifecycle,\n      previousLifecycle: previousState.lifecycle,\n      scrollToFirstStep,\n      step,\n      target,\n    });\n\n    if (status === STATUS.RUNNING && shouldScrollToStep) {\n      const hasCustomScroll = hasCustomScrollParent(target, disableScrollParentFix);\n      const scrollParent = getScrollParent(target, disableScrollParentFix);\n      let scrollY = Math.floor(getScrollTo(target, scrollOffset, disableScrollParentFix)) || 0;\n\n      log({\n        title: 'scrollToStep',\n        data: [\n          { key: 'index', value: index },\n          { key: 'lifecycle', value: lifecycle },\n          { key: 'status', value: status },\n        ],\n        debug,\n      });\n\n      const beaconPopper = this.store.getPopper('beacon');\n      const tooltipPopper = this.store.getPopper('tooltip');\n\n      if (lifecycle === LIFECYCLE.BEACON && beaconPopper) {\n        const { offsets, placement } = beaconPopper;\n\n        if (!['bottom'].includes(placement) && !hasCustomScroll) {\n          scrollY = Math.floor(offsets.popper.top - scrollOffset);\n        }\n      } else if (lifecycle === LIFECYCLE.TOOLTIP && tooltipPopper) {\n        const { flipped, offsets, placement } = tooltipPopper;\n\n        if (['top', 'right', 'left'].includes(placement) && !flipped && !hasCustomScroll) {\n          scrollY = Math.floor(offsets.popper.top - scrollOffset);\n        } else {\n          scrollY -= step.spotlightPadding;\n        }\n      }\n\n      scrollY = scrollY >= 0 ? scrollY : 0;\n\n      if (status === STATUS.RUNNING) {\n        scrollTo(scrollY, { element: scrollParent as Element, duration: scrollDuration }).then(\n          () => {\n            setTimeout(() => {\n              this.store.getPopper('tooltip')?.instance.update();\n            }, 10);\n          },\n        );\n      }\n    }\n  }\n\n  render() {\n    if (!canUseDOM()) {\n      return null;\n    }\n\n    const { index, lifecycle, status } = this.state;\n    const {\n      continuous = false,\n      debug = false,\n      nonce,\n      scrollToFirstStep = false,\n      steps,\n    } = this.props;\n    const isRunning = status === STATUS.RUNNING;\n    const content: Record<string, ReactNode> = {};\n\n    if (isRunning && steps[index]) {\n      const step = getMergedStep(this.props, steps[index]);\n\n      content.step = (\n        <Step\n          {...this.state}\n          callback={this.callback}\n          continuous={continuous}\n          debug={debug}\n          helpers={this.helpers}\n          nonce={nonce}\n          shouldScroll={!step.disableScrolling && (index !== 0 || scrollToFirstStep)}\n          step={step}\n          store={this.store}\n        />\n      );\n\n      content.overlay = (\n        <Portal id=\"react-joyride-portal\">\n          <Overlay\n            {...step}\n            continuous={continuous}\n            debug={debug}\n            lifecycle={lifecycle}\n            onClickOverlay={this.handleClickOverlay}\n          />\n        </Portal>\n      );\n    }\n\n    return (\n      <div className=\"react-joyride\">\n        {content.step}\n        {content.overlay}\n      </div>\n    );\n  }\n}\n\nexport default Joyride;\n", "import scroll from 'scroll';\nimport scrollParent from 'scrollparent';\n\nexport function canUseDOM() {\n  return !!(typeof window !== 'undefined' && window.document?.createElement);\n}\n\n/**\n * Find the bounding client rect\n */\nexport function getClientRect(element: HTMLElement | null) {\n  if (!element) {\n    return null;\n  }\n\n  return element.getBoundingClientRect();\n}\n\n/**\n * Helper function to get the browser-normalized \"document height\"\n */\nexport function getDocumentHeight(median = false): number {\n  const { body, documentElement } = document;\n\n  if (!body || !documentElement) {\n    return 0;\n  }\n\n  if (median) {\n    const heights = [\n      body.scrollHeight,\n      body.offsetHeight,\n      documentElement.clientHeight,\n      documentElement.scrollHeight,\n      documentElement.offsetHeight,\n    ].sort((a, b) => a - b);\n    const middle = Math.floor(heights.length / 2);\n\n    if (heights.length % 2 === 0) {\n      return (heights[middle - 1] + heights[middle]) / 2;\n    }\n\n    return heights[middle];\n  }\n\n  return Math.max(\n    body.scrollHeight,\n    body.offsetHeight,\n    documentElement.clientHeight,\n    documentElement.scrollHeight,\n    documentElement.offsetHeight,\n  );\n}\n\n/**\n * Find and return the target DOM element based on a step's 'target'.\n */\nexport function getElement(element: string | HTMLElement): HTMLElement | null {\n  if (typeof element === 'string') {\n    try {\n      return document.querySelector(element);\n    } catch (error: any) {\n      if (process.env.NODE_ENV !== 'production') {\n        // eslint-disable-next-line no-console\n        console.error(error);\n      }\n\n      return null;\n    }\n  }\n\n  return element;\n}\n\n/**\n *  Get computed style property\n */\nexport function getStyleComputedProperty(el: HTMLElement): CSSStyleDeclaration | null {\n  if (!el || el.nodeType !== 1) {\n    return null;\n  }\n\n  return getComputedStyle(el);\n}\n\n/**\n * Get scroll parent with fix\n */\nexport function getScrollParent(\n  element: HTMLElement | null,\n  skipFix: boolean,\n  forListener?: boolean,\n) {\n  if (!element) {\n    return scrollDocument();\n  }\n\n  const parent = scrollParent(element) as HTMLElement;\n\n  if (parent) {\n    if (parent.isSameNode(scrollDocument())) {\n      if (forListener) {\n        return document;\n      }\n\n      return scrollDocument();\n    }\n\n    const hasScrolling = parent.scrollHeight > parent.offsetHeight;\n\n    if (!hasScrolling && !skipFix) {\n      parent.style.overflow = 'initial';\n\n      return scrollDocument();\n    }\n  }\n\n  return parent;\n}\n\n/**\n * Check if the element has custom scroll parent\n */\nexport function hasCustomScrollParent(element: HTMLElement | null, skipFix: boolean): boolean {\n  if (!element) {\n    return false;\n  }\n\n  const parent = getScrollParent(element, skipFix);\n\n  return parent ? !parent.isSameNode(scrollDocument()) : false;\n}\n\n/**\n * Check if the element has custom offset parent\n */\nexport function hasCustomOffsetParent(element: HTMLElement): boolean {\n  return element.offsetParent !== document.body;\n}\n\n/**\n * Check if an element has fixed/sticky position\n */\nexport function hasPosition(el: HTMLElement | Node | null, type: string = 'fixed'): boolean {\n  if (!el || !(el instanceof HTMLElement)) {\n    return false;\n  }\n\n  const { nodeName } = el;\n  const styles = getStyleComputedProperty(el);\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n\n  if (styles && styles.position === type) {\n    return true;\n  }\n\n  if (!el.parentNode) {\n    return false;\n  }\n\n  return hasPosition(el.parentNode, type);\n}\n\n/**\n * Check if the element is visible\n */\nexport function isElementVisible(element: HTMLElement): element is HTMLElement {\n  if (!element) {\n    return false;\n  }\n\n  let parentElement: HTMLElement | null = element;\n\n  while (parentElement) {\n    if (parentElement === document.body) {\n      break;\n    }\n\n    if (parentElement instanceof HTMLElement) {\n      const { display, visibility } = getComputedStyle(parentElement);\n\n      if (display === 'none' || visibility === 'hidden') {\n        return false;\n      }\n    }\n\n    parentElement = parentElement.parentElement ?? null;\n  }\n\n  return true;\n}\n\n/**\n * Find and return the target DOM element based on a step's 'target'.\n */\nexport function getElementPosition(\n  element: HTMLElement | null,\n  offset: number,\n  skipFix: boolean,\n): number {\n  const elementRect = getClientRect(element);\n  const parent = getScrollParent(element, skipFix);\n  const hasScrollParent = hasCustomScrollParent(element, skipFix);\n  const isFixedTarget = hasPosition(element);\n  let parentTop = 0;\n  let top = elementRect?.top ?? 0;\n\n  if (hasScrollParent && isFixedTarget) {\n    const offsetTop = element?.offsetTop ?? 0;\n    const parentScrollTop = (parent as HTMLElement)?.scrollTop ?? 0;\n\n    top = offsetTop - parentScrollTop;\n  } else if (parent instanceof HTMLElement) {\n    parentTop = parent.scrollTop;\n\n    if (!hasScrollParent && !hasPosition(element)) {\n      top += parentTop;\n    }\n\n    if (!parent.isSameNode(scrollDocument())) {\n      top += scrollDocument().scrollTop;\n    }\n  }\n\n  return Math.floor(top - offset);\n}\n\n/**\n * Get the scrollTop position\n */\nexport function getScrollTo(element: HTMLElement | null, offset: number, skipFix: boolean): number {\n  if (!element) {\n    return 0;\n  }\n\n  const { offsetTop = 0, scrollTop = 0 } = scrollParent(element) ?? {};\n  let top = element.getBoundingClientRect().top + scrollTop;\n\n  if (!!offsetTop && (hasCustomScrollParent(element, skipFix) || hasCustomOffsetParent(element))) {\n    top -= offsetTop;\n  }\n\n  const output = Math.floor(top - offset);\n\n  return output < 0 ? 0 : output;\n}\n\nexport function scrollDocument(): Element | HTMLElement {\n  return document.scrollingElement ?? document.documentElement;\n}\n\n/**\n * Scroll to position\n */\nexport function scrollTo(\n  value: number,\n  options: { duration?: number; element: Element | HTMLElement },\n): Promise<void> {\n  const { duration, element } = options;\n\n  return new Promise((resolve, reject) => {\n    const { scrollTop } = element;\n\n    const limit = value > scrollTop ? value - scrollTop : scrollTop - value;\n\n    scroll.top(element as HTMLElement, value, { duration: limit < 100 ? 50 : duration }, error => {\n      if (error && error.message !== 'Element already at target scroll position') {\n        return reject(error);\n      }\n\n      return resolve();\n    });\n  });\n}\n", "import { cloneElement, FC, isValidElement, ReactElement, ReactNode } from 'react';\nimport { createPortal } from 'react-dom';\nimport innerText from 'react-innertext';\nimport is from 'is-lite';\n\nimport { LIFECYCLE } from '~/literals';\n\nimport { AnyObject, Lifecycle, NarrowPlainObject, Step } from '~/types';\n\nimport { hasPosition } from './dom';\n\ninterface GetReactNodeTextOptions {\n  defaultValue?: any;\n  step?: number;\n  steps?: number;\n}\n\ninterface LogOptions {\n  /** The data to be logged */\n  data: any;\n  /** display the log */\n  debug?: boolean;\n  /** The title the logger was called from */\n  title: string;\n  /** If true, the message will be a warning */\n  warn?: boolean;\n}\n\ninterface ShouldScrollOptions {\n  isFirstStep: boolean;\n  lifecycle: Lifecycle;\n  previousLifecycle: Lifecycle;\n  scrollToFirstStep: boolean;\n  step: Step;\n  target: HTMLElement | null;\n}\n\nexport const isReact16 = createPortal !== undefined;\n\n/**\n * Get the current browser\n */\nexport function getBrowser(userAgent: string = navigator.userAgent): string {\n  let browser = userAgent;\n\n  if (typeof window === 'undefined') {\n    browser = 'node';\n  }\n  // @ts-expect-error IE support\n  else if (document.documentMode) {\n    browser = 'ie';\n  } else if (/Edge/.test(userAgent)) {\n    browser = 'edge';\n  }\n  // @ts-expect-error Opera 8.0+\n  else if (Boolean(window.opera) || userAgent.includes(' OPR/')) {\n    browser = 'opera';\n  }\n  // @ts-expect-error Firefox 1.0+\n  else if (typeof window.InstallTrigger !== 'undefined') {\n    browser = 'firefox';\n  }\n  // @ts-expect-error Chrome 1+\n  else if (window.chrome) {\n    browser = 'chrome';\n  }\n  // Safari (and Chrome iOS, Firefox iOS)\n  else if (/(Version\\/([\\d._]+).*Safari|CriOS|FxiOS| Mobile\\/)/.test(userAgent)) {\n    browser = 'safari';\n  }\n\n  return browser;\n}\n\n/**\n * Get Object type\n */\nexport function getObjectType(value: unknown): string {\n  return Object.prototype.toString.call(value).slice(8, -1).toLowerCase();\n}\n\nexport function getReactNodeText(input: ReactNode, options: GetReactNodeTextOptions = {}): string {\n  const { defaultValue, step, steps } = options;\n  let text = innerText(input);\n\n  if (!text) {\n    if (\n      isValidElement(input) &&\n      !Object.values(input.props).length &&\n      getObjectType(input.type) === 'function'\n    ) {\n      const component = (input.type as FC)({});\n\n      text = getReactNodeText(component, options);\n    } else {\n      text = innerText(defaultValue);\n    }\n  } else if ((text.includes('{step}') || text.includes('{steps}')) && step && steps) {\n    text = text.replace('{step}', step.toString()).replace('{steps}', steps.toString());\n  }\n\n  return text;\n}\n\nexport function hasValidKeys(object: Record<string, unknown>, keys?: Array<string>): boolean {\n  if (!is.plainObject(object) || !is.array(keys)) {\n    return false;\n  }\n\n  return Object.keys(object).every(d => keys.includes(d));\n}\n\n/**\n * Convert hex to RGB\n */\nexport function hexToRGB(hex: string): Array<number> {\n  const shorthandRegex = /^#?([\\da-f])([\\da-f])([\\da-f])$/i;\n  const properHex = hex.replace(shorthandRegex, (_m, r, g, b) => r + r + g + g + b + b);\n\n  const result = /^#?([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/i.exec(properHex);\n\n  return result ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)] : [];\n}\n\n/**\n * Decide if the step shouldn't skip the beacon\n * @param {Object} step\n *\n * @returns {boolean}\n */\nexport function hideBeacon(step: Step): boolean {\n  return step.disableBeacon || step.placement === 'center';\n}\n\n/**\n * Detect legacy browsers\n *\n * @returns {boolean}\n */\nexport function isLegacy(): boolean {\n  return !['chrome', 'safari', 'firefox', 'opera'].includes(getBrowser());\n}\n\n/**\n * Log method calls if debug is enabled\n */\nexport function log({ data, debug = false, title, warn = false }: LogOptions) {\n  /* eslint-disable no-console */\n  const logFn = warn ? console.warn || console.error : console.log;\n\n  if (debug) {\n    if (title && data) {\n      console.groupCollapsed(\n        `%creact-joyride: ${title}`,\n        'color: #ff0044; font-weight: bold; font-size: 12px;',\n      );\n\n      if (Array.isArray(data)) {\n        data.forEach(d => {\n          if (is.plainObject(d) && d.key) {\n            logFn.apply(console, [d.key, d.value]);\n          } else {\n            logFn.apply(console, [d]);\n          }\n        });\n      } else {\n        logFn.apply(console, [data]);\n      }\n\n      console.groupEnd();\n    } else {\n      console.error('Missing title or data props');\n    }\n  }\n  /* eslint-enable */\n}\n\n/**\n * A function that does nothing.\n */\nexport function noop() {\n  return undefined;\n}\n\n/**\n * Type-safe Object.keys()\n */\nexport function objectKeys<T extends AnyObject>(input: T) {\n  return Object.keys(input) as Array<keyof T>;\n}\n\n/**\n * Remove properties from an object\n */\nexport function omit<T extends Record<string, any>, K extends keyof T>(\n  input: NarrowPlainObject<T>,\n  ...filter: K[]\n) {\n  if (!is.plainObject(input)) {\n    throw new TypeError('Expected an object');\n  }\n\n  const output: any = {};\n\n  for (const key in input) {\n    /* istanbul ignore else */\n    if ({}.hasOwnProperty.call(input, key)) {\n      if (!filter.includes(key as unknown as K)) {\n        output[key] = input[key];\n      }\n    }\n  }\n\n  return output as Omit<T, K>;\n}\n\n/**\n * Select properties from an object\n */\nexport function pick<T extends Record<string, any>, K extends keyof T>(\n  input: NarrowPlainObject<T>,\n  ...filter: K[]\n) {\n  if (!is.plainObject(input)) {\n    throw new TypeError('Expected an object');\n  }\n\n  if (!filter.length) {\n    return input;\n  }\n\n  const output: any = {};\n\n  for (const key in input) {\n    /* istanbul ignore else */\n    if ({}.hasOwnProperty.call(input, key)) {\n      if (filter.includes(key as unknown as K)) {\n        output[key] = input[key];\n      }\n    }\n  }\n\n  return output as Pick<T, K>;\n}\n\nexport function replaceLocaleContent(input: ReactNode, step: number, steps: number): ReactNode {\n  const replacer = (text: string) =>\n    text.replace('{step}', String(step)).replace('{steps}', String(steps));\n\n  if (getObjectType(input) === 'string') {\n    return replacer(input as string);\n  }\n\n  if (!isValidElement(input)) {\n    return input;\n  }\n\n  const { children } = input.props;\n\n  if (getObjectType(children) === 'string' && children.includes('{step}')) {\n    return cloneElement(input as ReactElement, {\n      children: replacer(children),\n    });\n  }\n\n  if (Array.isArray(children)) {\n    return cloneElement(input as ReactElement, {\n      children: children.map((child: ReactNode) => {\n        if (typeof child === 'string') {\n          return replacer(child);\n        }\n\n        return replaceLocaleContent(child, step, steps);\n      }),\n    });\n  }\n\n  if (getObjectType(input.type) === 'function' && !Object.values(input.props).length) {\n    const component = (input.type as FC)({});\n\n    return replaceLocaleContent(component, step, steps);\n  }\n\n  return input;\n}\n\nexport function shouldScroll(options: ShouldScrollOptions): boolean {\n  const { isFirstStep, lifecycle, previousLifecycle, scrollToFirstStep, step, target } = options;\n\n  return (\n    !step.disableScrolling &&\n    (!isFirstStep || scrollToFirstStep || lifecycle === LIFECYCLE.TOOLTIP) &&\n    step.placement !== 'center' &&\n    (!step.isFixed || !hasPosition(target)) && // fixed steps don't need to scroll\n    previousLifecycle !== lifecycle &&\n    ([LIFECYCLE.BEACON, LIFECYCLE.TOOLTIP] as Array<Lifecycle>).includes(lifecycle)\n  );\n}\n\n/**\n * Block execution\n */\nexport function sleep(seconds = 1) {\n  return new Promise(resolve => {\n    setTimeout(resolve, seconds * 1000);\n  });\n}\n", "import { Props as FloaterProps } from 'react-floater';\nimport deepmerge from 'deepmerge';\nimport is from 'is-lite';\nimport { SetRequired } from 'type-fest';\n\nimport { defaultFloaterProps, defaultLocale, defaultStep } from '~/defaults';\nimport getStyles from '~/styles';\nimport { Props, Step, StepMerged } from '~/types';\n\nimport { getElement, hasCustomScrollParent } from './dom';\nimport { log, omit, pick } from './helpers';\n\nfunction getTourProps(props: Props) {\n  return pick(\n    props,\n    'beaconComponent',\n    'disableCloseOnEsc',\n    'disableOverlay',\n    'disableOverlayClose',\n    'disableScrolling',\n    'disableScrollParentFix',\n    'floaterProps',\n    'hideBackButton',\n    'hideCloseButton',\n    'locale',\n    'showProgress',\n    'showSkipButton',\n    'spotlightClicks',\n    'spotlightPadding',\n    'styles',\n    'tooltipComponent',\n  );\n}\n\nexport function getMergedStep(props: Props, currentStep?: Step): StepMerged {\n  const step = currentStep ?? {};\n  const mergedStep = deepmerge.all([defaultStep, getTourProps(props), step], {\n    isMergeableObject: is.plainObject,\n  }) as StepMerged;\n\n  const mergedStyles = getStyles(props, mergedStep);\n  const scrollParent = hasCustomScrollParent(\n    getElement(mergedStep.target),\n    mergedStep.disableScrollParentFix,\n  );\n  const floaterProps = deepmerge.all([\n    defaultFloaterProps,\n    props.floaterProps ?? {},\n    mergedStep.floaterProps ?? {},\n  ]) as SetRequired<FloaterProps, 'options' | 'wrapperOptions'>;\n\n  // Set react-floater props\n  floaterProps.offset = mergedStep.offset;\n  floaterProps.styles = deepmerge(floaterProps.styles ?? {}, mergedStyles.floaterStyles);\n\n  floaterProps.offset += props.spotlightPadding ?? mergedStep.spotlightPadding ?? 0;\n\n  if (mergedStep.placementBeacon && floaterProps.wrapperOptions) {\n    floaterProps.wrapperOptions.placement = mergedStep.placementBeacon;\n  }\n\n  if (scrollParent && floaterProps.options.preventOverflow) {\n    floaterProps.options.preventOverflow.boundariesElement = 'window';\n  }\n\n  return {\n    ...mergedStep,\n    locale: deepmerge.all([defaultLocale, props.locale ?? {}, mergedStep.locale || {}]),\n    floaterProps,\n    styles: omit(mergedStyles, 'floaterStyles'),\n  };\n}\n\n/**\n * Validate if a step is valid\n */\nexport function validateStep(step: Step, debug: boolean = false): boolean {\n  if (!is.plainObject(step)) {\n    log({\n      title: 'validateStep',\n      data: 'step must be an object',\n      warn: true,\n      debug,\n    });\n\n    return false;\n  }\n\n  if (!step.target) {\n    log({\n      title: 'validateStep',\n      data: 'target is missing from the step',\n      warn: true,\n      debug,\n    });\n\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Validate if steps are valid\n */\nexport function validateSteps(steps: Array<Step>, debug: boolean = false): boolean {\n  if (!is.array(steps)) {\n    log({\n      title: 'validateSteps',\n      data: 'steps must be an array',\n      warn: true,\n      debug,\n    });\n\n    return false;\n  }\n\n  return steps.every(d => validateStep(d, debug));\n}\n", "import { noop } from '~/modules/helpers';\n\nimport { FloaterProps, Locale, Props, Step } from '~/types';\n\nexport const defaultFloaterProps: FloaterProps = {\n  options: {\n    preventOverflow: {\n      boundariesElement: 'scrollParent',\n    },\n  },\n  wrapperOptions: {\n    offset: -18,\n    position: true,\n  },\n};\n\nexport const defaultLocale: Locale = {\n  back: 'Back',\n  close: 'Close',\n  last: 'Last',\n  next: 'Next',\n  nextLabelWithProgress: 'Next (Step {step} of {steps})',\n  open: 'Open the dialog',\n  skip: 'Skip',\n};\n\nexport const defaultStep = {\n  event: 'click',\n  placement: 'bottom',\n  offset: 10,\n  disableBeacon: false,\n  disableCloseOnEsc: false,\n  disableOverlay: false,\n  disableOverlayClose: false,\n  disableScrollParentFix: false,\n  disableScrolling: false,\n  hideBackButton: false,\n  hideCloseButton: false,\n  hideFooter: false,\n  isFixed: false,\n  locale: defaultLocale,\n  showProgress: false,\n  showSkipButton: false,\n  spotlightClicks: false,\n  spotlightPadding: 10,\n} satisfies Omit<Step, 'content' | 'target'>;\n\nexport const defaultProps = {\n  continuous: false,\n  debug: false,\n  disableCloseOnEsc: false,\n  disableOverlay: false,\n  disableOverlayClose: false,\n  disableScrolling: false,\n  disableScrollParentFix: false,\n  getHelpers: noop(),\n  hideBackButton: false,\n  run: true,\n  scrollOffset: 20,\n  scrollDuration: 300,\n  scrollToFirstStep: false,\n  showSkipButton: false,\n  showProgress: false,\n  spotlightClicks: false,\n  spotlightPadding: 10,\n  steps: [],\n} satisfies Props;\n", "import deepmerge from 'deepmerge';\n\nimport { hexToRGB } from './modules/helpers';\nimport { Props, StepMerged, StylesOptions, StylesWithFloaterStyles } from './types';\n\nconst defaultOptions = {\n  arrowColor: '#fff',\n  backgroundColor: '#fff',\n  beaconSize: 36,\n  overlayColor: 'rgba(0, 0, 0, 0.5)',\n  primaryColor: '#f04',\n  spotlightShadow: '0 0 15px rgba(0, 0, 0, 0.5)',\n  textColor: '#333',\n  width: 380,\n  zIndex: 100,\n} satisfies StylesOptions;\n\nconst buttonBase = {\n  backgroundColor: 'transparent',\n  border: 0,\n  borderRadius: 0,\n  color: '#555',\n  cursor: 'pointer',\n  fontSize: 16,\n  lineHeight: 1,\n  padding: 8,\n  WebkitAppearance: 'none',\n};\n\nconst spotlight = {\n  borderRadius: 4,\n  position: 'absolute',\n};\n\nexport default function getStyles(props: Props, step: StepMerged) {\n  const { floaterProps, styles } = props;\n  const mergedFloaterProps = deepmerge(step.floaterProps ?? {}, floaterProps ?? {});\n  const mergedStyles = deepmerge(styles ?? {}, step.styles ?? {});\n  const options = deepmerge(defaultOptions, mergedStyles.options || {}) satisfies StylesOptions;\n  const hideBeacon = step.placement === 'center' || step.disableBeacon;\n  let { width } = options;\n\n  if (window.innerWidth > 480) {\n    width = 380;\n  }\n\n  if ('width' in options) {\n    width =\n      typeof options.width === 'number' && window.innerWidth < options.width\n        ? window.innerWidth - 30\n        : options.width;\n  }\n\n  const overlay = {\n    bottom: 0,\n    left: 0,\n    overflow: 'hidden',\n    position: 'absolute',\n    right: 0,\n    top: 0,\n    zIndex: options.zIndex,\n  };\n\n  const defaultStyles = {\n    beacon: {\n      ...buttonBase,\n      display: hideBeacon ? 'none' : 'inline-block',\n      height: options.beaconSize,\n      position: 'relative',\n      width: options.beaconSize,\n      zIndex: options.zIndex,\n    },\n    beaconInner: {\n      animation: 'joyride-beacon-inner 1.2s infinite ease-in-out',\n      backgroundColor: options.primaryColor,\n      borderRadius: '50%',\n      display: 'block',\n      height: '50%',\n      left: '50%',\n      opacity: 0.7,\n      position: 'absolute',\n      top: '50%',\n      transform: 'translate(-50%, -50%)',\n      width: '50%',\n    },\n    beaconOuter: {\n      animation: 'joyride-beacon-outer 1.2s infinite ease-in-out',\n      backgroundColor: `rgba(${hexToRGB(options.primaryColor).join(',')}, 0.2)`,\n      border: `2px solid ${options.primaryColor}`,\n      borderRadius: '50%',\n      boxSizing: 'border-box',\n      display: 'block',\n      height: '100%',\n      left: 0,\n      opacity: 0.9,\n      position: 'absolute',\n      top: 0,\n      transformOrigin: 'center',\n      width: '100%',\n    },\n    tooltip: {\n      backgroundColor: options.backgroundColor,\n      borderRadius: 5,\n      boxSizing: 'border-box',\n      color: options.textColor,\n      fontSize: 16,\n      maxWidth: '100%',\n      padding: 15,\n      position: 'relative',\n      width,\n    },\n    tooltipContainer: {\n      lineHeight: 1.4,\n      textAlign: 'center',\n    },\n    tooltipTitle: {\n      fontSize: 18,\n      margin: 0,\n    },\n    tooltipContent: {\n      padding: '20px 10px',\n    },\n    tooltipFooter: {\n      alignItems: 'center',\n      display: 'flex',\n      justifyContent: 'flex-end',\n      marginTop: 15,\n    },\n    tooltipFooterSpacer: {\n      flex: 1,\n    },\n    buttonNext: {\n      ...buttonBase,\n      backgroundColor: options.primaryColor,\n      borderRadius: 4,\n      color: '#fff',\n    },\n    buttonBack: {\n      ...buttonBase,\n      color: options.primaryColor,\n      marginLeft: 'auto',\n      marginRight: 5,\n    },\n    buttonClose: {\n      ...buttonBase,\n      color: options.textColor,\n      height: 14,\n      padding: 15,\n      position: 'absolute',\n      right: 0,\n      top: 0,\n      width: 14,\n    },\n    buttonSkip: {\n      ...buttonBase,\n      color: options.textColor,\n      fontSize: 14,\n    },\n    overlay: {\n      ...overlay,\n      backgroundColor: options.overlayColor,\n      mixBlendMode: 'hard-light',\n    },\n    overlayLegacy: {\n      ...overlay,\n    },\n    overlayLegacyCenter: {\n      ...overlay,\n      backgroundColor: options.overlayColor,\n    },\n    spotlight: {\n      ...spotlight,\n      backgroundColor: 'gray',\n    },\n    spotlightLegacy: {\n      ...spotlight,\n      boxShadow: `0 0 0 9999px ${options.overlayColor}, ${options.spotlightShadow}`,\n    },\n    floaterStyles: {\n      arrow: {\n        color: mergedFloaterProps?.styles?.arrow?.color ?? options.arrowColor,\n      },\n      options: {\n        zIndex: options.zIndex + 100,\n      },\n    },\n    options,\n  };\n\n  return deepmerge(defaultStyles, mergedStyles) as StylesWithFloaterStyles;\n}\n", "import { Props as FloaterProps } from 'react-floater';\nimport is from 'is-lite';\n\nimport { ACTIONS, LIFECYCLE, STATUS } from '~/literals';\n\nimport { Origin, State, Status, Step, StoreHelpers, StoreOptions } from '~/types';\n\nimport { hasValidKeys, objectKeys, omit } from './helpers';\n\ntype StateWithContinuous = State & { continuous: boolean };\ntype Listener = (state: State) => void;\ntype PopperData = Parameters<NonNullable<FloaterProps['getPopper']>>[0];\n\nconst defaultState: State = {\n  action: 'init',\n  controlled: false,\n  index: 0,\n  lifecycle: LIFECYCLE.INIT,\n  origin: null,\n  size: 0,\n  status: STATUS.IDLE,\n};\nconst validKeys = objectKeys(omit(defaultState, 'controlled', 'size'));\n\nclass Store {\n  private beaconPopper: PopperData | null;\n  private tooltipPopper: PopperData | null;\n  private data: Map<string, any> = new Map();\n  private listener: Listener | null;\n  private store: Map<string, any> = new Map();\n\n  constructor(options?: StoreOptions) {\n    const { continuous = false, stepIndex, steps = [] } = options ?? {};\n\n    this.setState(\n      {\n        action: ACTIONS.INIT,\n        controlled: is.number(stepIndex),\n        continuous,\n        index: is.number(stepIndex) ? stepIndex : 0,\n        lifecycle: LIFECYCLE.INIT,\n        origin: null,\n        status: steps.length ? STATUS.READY : STATUS.IDLE,\n      },\n      true,\n    );\n\n    this.beaconPopper = null;\n    this.tooltipPopper = null;\n    this.listener = null;\n    this.setSteps(steps);\n  }\n\n  public getState(): State {\n    if (!this.store.size) {\n      return { ...defaultState };\n    }\n\n    return {\n      action: this.store.get('action') || '',\n      controlled: this.store.get('controlled') || false,\n      index: parseInt(this.store.get('index'), 10),\n      lifecycle: this.store.get('lifecycle') || '',\n      origin: this.store.get('origin') || null,\n      size: this.store.get('size') || 0,\n      status: (this.store.get('status') as Status) || '',\n    };\n  }\n\n  private getNextState(state: Partial<State>, force: boolean = false): State {\n    const { action, controlled, index, size, status } = this.getState();\n    const newIndex = is.number(state.index) ? state.index : index;\n    const nextIndex = controlled && !force ? index : Math.min(Math.max(newIndex, 0), size);\n\n    return {\n      action: state.action ?? action,\n      controlled,\n      index: nextIndex,\n      lifecycle: state.lifecycle ?? LIFECYCLE.INIT,\n      origin: state.origin ?? null,\n      size: state.size ?? size,\n      status: nextIndex === size ? STATUS.FINISHED : (state.status ?? status),\n    };\n  }\n\n  private getSteps(): Array<Step> {\n    const steps = this.data.get('steps');\n\n    return Array.isArray(steps) ? steps : [];\n  }\n\n  private hasUpdatedState(oldState: State): boolean {\n    const before = JSON.stringify(oldState);\n    const after = JSON.stringify(this.getState());\n\n    return before !== after;\n  }\n\n  private setState(nextState: Partial<StateWithContinuous>, initial: boolean = false) {\n    const state = this.getState();\n\n    const {\n      action,\n      index,\n      lifecycle,\n      origin = null,\n      size,\n      status,\n    } = {\n      ...state,\n      ...nextState,\n    };\n\n    this.store.set('action', action);\n    this.store.set('index', index);\n    this.store.set('lifecycle', lifecycle);\n    this.store.set('origin', origin);\n    this.store.set('size', size);\n    this.store.set('status', status);\n\n    if (initial) {\n      this.store.set('controlled', nextState.controlled);\n      this.store.set('continuous', nextState.continuous);\n    }\n\n    if (this.listener && this.hasUpdatedState(state)) {\n      this.listener(this.getState());\n    }\n  }\n\n  public addListener = (listener: Listener) => {\n    this.listener = listener;\n  };\n\n  public setSteps = (steps: Array<Step>) => {\n    const { size, status } = this.getState();\n    const state = {\n      size: steps.length,\n      status,\n    };\n\n    this.data.set('steps', steps);\n\n    if (status === STATUS.WAITING && !size && steps.length) {\n      state.status = STATUS.RUNNING;\n    }\n\n    this.setState(state);\n  };\n\n  public getHelpers(): StoreHelpers {\n    return {\n      close: this.close,\n      go: this.go,\n      info: this.info,\n      next: this.next,\n      open: this.open,\n      prev: this.prev,\n      reset: this.reset,\n      skip: this.skip,\n    };\n  }\n\n  public getPopper = (name: 'beacon' | 'tooltip'): PopperData | null => {\n    if (name === 'beacon') {\n      return this.beaconPopper;\n    }\n\n    return this.tooltipPopper;\n  };\n\n  public setPopper = (name: 'beacon' | 'tooltip', popper: PopperData) => {\n    if (name === 'beacon') {\n      this.beaconPopper = popper;\n    } else {\n      this.tooltipPopper = popper;\n    }\n  };\n\n  public cleanupPoppers = () => {\n    this.beaconPopper = null;\n    this.tooltipPopper = null;\n  };\n\n  public close = (origin: Origin | null = null) => {\n    const { index, status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.CLOSE, index: index + 1, origin }),\n    });\n  };\n\n  public go = (nextIndex: number) => {\n    const { controlled, status } = this.getState();\n\n    if (controlled || status !== STATUS.RUNNING) {\n      return;\n    }\n\n    const step = this.getSteps()[nextIndex];\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.GO, index: nextIndex }),\n      status: step ? status : STATUS.FINISHED,\n    });\n  };\n\n  public info = (): State => this.getState();\n\n  public next = () => {\n    const { index, status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState(this.getNextState({ action: ACTIONS.NEXT, index: index + 1 }));\n  };\n\n  public open = () => {\n    const { status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.UPDATE, lifecycle: LIFECYCLE.TOOLTIP }),\n    });\n  };\n\n  public prev = () => {\n    const { index, status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.PREV, index: index - 1 }),\n    });\n  };\n\n  public reset = (restart = false) => {\n    const { controlled } = this.getState();\n\n    if (controlled) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.RESET, index: 0 }),\n      status: restart ? STATUS.RUNNING : STATUS.READY,\n    });\n  };\n\n  public skip = () => {\n    const { status } = this.getState();\n\n    if (status !== STATUS.RUNNING) {\n      return;\n    }\n\n    this.setState({\n      action: ACTIONS.SKIP,\n      lifecycle: LIFECYCLE.INIT,\n      status: STATUS.SKIPPED,\n    });\n  };\n\n  public start = (nextIndex?: number) => {\n    const { index, size } = this.getState();\n\n    this.setState({\n      ...this.getNextState(\n        {\n          action: ACTIONS.START,\n          index: is.number(nextIndex) ? nextIndex : index,\n        },\n        true,\n      ),\n      status: size ? STATUS.RUNNING : STATUS.WAITING,\n    });\n  };\n\n  public stop = (advance = false) => {\n    const { index, status } = this.getState();\n\n    if (([STATUS.FINISHED, STATUS.SKIPPED] as Array<Status>).includes(status)) {\n      return;\n    }\n\n    this.setState({\n      ...this.getNextState({ action: ACTIONS.STOP, index: index + (advance ? 1 : 0) }),\n      status: STATUS.PAUSED,\n    });\n  };\n\n  public update = (state: Partial<State>) => {\n    if (!hasValidKeys(state, validKeys)) {\n      throw new Error(`State is not valid. Valid keys: ${validKeys.join(', ')}`);\n    }\n\n    this.setState({\n      ...this.getNextState(\n        {\n          ...this.getState(),\n          ...state,\n          action: state.action ?? ACTIONS.UPDATE,\n          origin: state.origin ?? null,\n        },\n        true,\n      ),\n    });\n  };\n}\n\nexport type StoreInstance = ReturnType<typeof createStore>;\n\nexport default function createStore(options?: StoreOptions) {\n  return new Store(options);\n}\n", "import * as React from 'react';\nimport treeChanges from 'tree-changes';\n\nimport {\n  getClientRect,\n  getDocumentHeight,\n  getElement,\n  getElementPosition,\n  getScrollParent,\n  hasCustomScrollParent,\n  hasPosition,\n} from '~/modules/dom';\nimport { getBrowser, isLegacy, log } from '~/modules/helpers';\n\nimport { LIFECYCLE } from '~/literals';\n\nimport { Lifecycle, OverlayProps } from '~/types';\n\nimport Spotlight from './Spotlight';\n\ninterface State {\n  isScrolling: boolean;\n  mouseOverSpotlight: boolean;\n  showSpotlight: boolean;\n}\n\ninterface SpotlightStyles extends React.CSSProperties {\n  height: number;\n  left: number;\n  top: number;\n  width: number;\n}\n\nexport default class JoyrideOverlay extends React.Component<OverlayProps, State> {\n  isActive = false;\n  resizeTimeout?: number;\n  scrollTimeout?: number;\n  scrollParent?: Document | Element;\n  state = {\n    isScrolling: false,\n    mouseOverSpotlight: false,\n    showSpotlight: true,\n  };\n\n  componentDidMount() {\n    const { debug, disableScrolling, disableScrollParentFix = false, target } = this.props;\n    const element = getElement(target);\n\n    this.scrollParent = getScrollParent(element ?? document.body, disableScrollParentFix, true);\n    this.isActive = true;\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!disableScrolling && hasCustomScrollParent(element, true)) {\n        log({\n          title: 'step has a custom scroll parent and can cause trouble with scrolling',\n          data: [{ key: 'parent', value: this.scrollParent }],\n          debug,\n        });\n      }\n    }\n\n    window.addEventListener('resize', this.handleResize);\n  }\n\n  componentDidUpdate(previousProps: OverlayProps) {\n    const { disableScrollParentFix, lifecycle, spotlightClicks, target } = this.props;\n    const { changed } = treeChanges(previousProps, this.props);\n\n    if (changed('target') || changed('disableScrollParentFix')) {\n      const element = getElement(target);\n\n      this.scrollParent = getScrollParent(element ?? document.body, disableScrollParentFix, true);\n    }\n\n    if (changed('lifecycle', LIFECYCLE.TOOLTIP)) {\n      this.scrollParent?.addEventListener('scroll', this.handleScroll, { passive: true });\n\n      setTimeout(() => {\n        const { isScrolling } = this.state;\n\n        if (!isScrolling) {\n          this.updateState({ showSpotlight: true });\n        }\n      }, 100);\n    }\n\n    if (changed('spotlightClicks') || changed('disableOverlay') || changed('lifecycle')) {\n      if (spotlightClicks && lifecycle === LIFECYCLE.TOOLTIP) {\n        window.addEventListener('mousemove', this.handleMouseMove, false);\n      } else if (lifecycle !== LIFECYCLE.TOOLTIP) {\n        window.removeEventListener('mousemove', this.handleMouseMove);\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    this.isActive = false;\n\n    window.removeEventListener('mousemove', this.handleMouseMove);\n    window.removeEventListener('resize', this.handleResize);\n\n    clearTimeout(this.resizeTimeout);\n    clearTimeout(this.scrollTimeout);\n    this.scrollParent?.removeEventListener('scroll', this.handleScroll);\n  }\n\n  hideSpotlight = () => {\n    const { continuous, disableOverlay, lifecycle } = this.props;\n    const hiddenLifecycles = [\n      LIFECYCLE.INIT,\n      LIFECYCLE.BEACON,\n      LIFECYCLE.COMPLETE,\n      LIFECYCLE.ERROR,\n    ] as Lifecycle[];\n\n    return (\n      disableOverlay ||\n      (continuous ? hiddenLifecycles.includes(lifecycle) : lifecycle !== LIFECYCLE.TOOLTIP)\n    );\n  };\n\n  get overlayStyles() {\n    const { mouseOverSpotlight } = this.state;\n    const { disableOverlayClose, placement, styles } = this.props;\n\n    let baseStyles = styles.overlay;\n\n    if (isLegacy()) {\n      baseStyles = placement === 'center' ? styles.overlayLegacyCenter : styles.overlayLegacy;\n    }\n\n    return {\n      cursor: disableOverlayClose ? 'default' : 'pointer',\n      height: getDocumentHeight(),\n      pointerEvents: mouseOverSpotlight ? 'none' : 'auto',\n      ...baseStyles,\n    } as React.CSSProperties;\n  }\n\n  get spotlightStyles(): SpotlightStyles {\n    const { showSpotlight } = this.state;\n    const {\n      disableScrollParentFix = false,\n      spotlightClicks,\n      spotlightPadding = 0,\n      styles,\n      target,\n    } = this.props;\n    const element = getElement(target);\n    const elementRect = getClientRect(element);\n    const isFixedTarget = hasPosition(element);\n    const top = getElementPosition(element, spotlightPadding, disableScrollParentFix);\n\n    return {\n      ...(isLegacy() ? styles.spotlightLegacy : styles.spotlight),\n      height: Math.round((elementRect?.height ?? 0) + spotlightPadding * 2),\n      left: Math.round((elementRect?.left ?? 0) - spotlightPadding),\n      opacity: showSpotlight ? 1 : 0,\n      pointerEvents: spotlightClicks ? 'none' : 'auto',\n      position: isFixedTarget ? 'fixed' : 'absolute',\n      top,\n      transition: 'opacity 0.2s',\n      width: Math.round((elementRect?.width ?? 0) + spotlightPadding * 2),\n    } satisfies React.CSSProperties;\n  }\n\n  handleMouseMove = (event: MouseEvent) => {\n    const { mouseOverSpotlight } = this.state;\n    const { height, left, position, top, width } = this.spotlightStyles;\n\n    const offsetY = position === 'fixed' ? event.clientY : event.pageY;\n    const offsetX = position === 'fixed' ? event.clientX : event.pageX;\n    const inSpotlightHeight = offsetY >= top && offsetY <= top + height;\n    const inSpotlightWidth = offsetX >= left && offsetX <= left + width;\n    const inSpotlight = inSpotlightWidth && inSpotlightHeight;\n\n    if (inSpotlight !== mouseOverSpotlight) {\n      this.updateState({ mouseOverSpotlight: inSpotlight });\n    }\n  };\n\n  handleScroll = () => {\n    const { target } = this.props;\n    const element = getElement(target);\n\n    if (this.scrollParent !== document) {\n      const { isScrolling } = this.state;\n\n      if (!isScrolling) {\n        this.updateState({ isScrolling: true, showSpotlight: false });\n      }\n\n      clearTimeout(this.scrollTimeout);\n\n      this.scrollTimeout = window.setTimeout(() => {\n        this.updateState({ isScrolling: false, showSpotlight: true });\n      }, 50);\n    } else if (hasPosition(element, 'sticky')) {\n      this.updateState({});\n    }\n  };\n\n  handleResize = () => {\n    clearTimeout(this.resizeTimeout);\n\n    this.resizeTimeout = window.setTimeout(() => {\n      if (!this.isActive) {\n        return;\n      }\n\n      this.forceUpdate();\n    }, 100);\n  };\n\n  updateState(state: Partial<State>) {\n    if (!this.isActive) {\n      return;\n    }\n\n    this.setState(previousState => ({ ...previousState, ...state }));\n  }\n\n  render() {\n    const { showSpotlight } = this.state;\n    const { onClickOverlay, placement } = this.props;\n    const { hideSpotlight, overlayStyles, spotlightStyles } = this;\n\n    if (hideSpotlight()) {\n      return null;\n    }\n\n    let spotlight = placement !== 'center' && showSpotlight && (\n      <Spotlight styles={spotlightStyles} />\n    );\n\n    // Hack for Safari bug with mix-blend-mode with z-index\n    if (getBrowser() === 'safari') {\n      const { mixBlendMode, zIndex, ...safariOverlay } = overlayStyles;\n\n      spotlight = <div style={{ ...safariOverlay }}>{spotlight}</div>;\n      delete overlayStyles.backgroundColor;\n    }\n\n    return (\n      <div\n        className=\"react-joyride__overlay\"\n        data-test-id=\"overlay\"\n        onClick={onClickOverlay}\n        role=\"presentation\"\n        style={overlayStyles}\n      >\n        {spotlight}\n      </div>\n    );\n  }\n}\n", "import * as React from 'react';\n\ninterface Props {\n  styles: React.CSSProperties;\n}\n\nfunction JoyrideSpotlight({ styles }: Props) {\n  return (\n    <div\n      key=\"JoyrideSpotlight\"\n      className=\"react-joyride__spotlight\"\n      data-test-id=\"spotlight\"\n      style={styles}\n    />\n  );\n}\n\nexport default JoyrideSpotlight;\n", "import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\n\nimport { canUseDOM } from '~/modules/dom';\nimport { isReact16 } from '~/modules/helpers';\n\ninterface Props {\n  children: React.ReactElement;\n  id: string;\n}\n\nexport default class JoyridePortal extends React.Component<Props> {\n  node: HTMLElement | null = null;\n\n  componentDidMount() {\n    const { id } = this.props;\n\n    if (!canUseDOM()) {\n      return;\n    }\n\n    this.node = document.createElement('div');\n    this.node.id = id;\n\n    document.body.appendChild(this.node);\n\n    if (!isReact16) {\n      this.renderReact15();\n    }\n  }\n\n  componentDidUpdate() {\n    if (!canUseDOM()) {\n      return;\n    }\n\n    if (!isReact16) {\n      this.renderReact15();\n    }\n  }\n\n  componentWillUnmount() {\n    if (!canUseDOM() || !this.node) {\n      return;\n    }\n\n    if (!isReact16) {\n      // eslint-disable-next-line react/no-deprecated\n      ReactDOM.unmountComponentAtNode(this.node);\n    }\n\n    if (this.node.parentNode === document.body) {\n      document.body.removeChild(this.node);\n      this.node = null;\n    }\n  }\n\n  renderReact15() {\n    if (!canUseDOM()) {\n      return;\n    }\n\n    const { children } = this.props;\n\n    if (this.node) {\n      ReactDOM.unstable_renderSubtreeIntoContainer(this, children, this.node);\n    }\n  }\n\n  renderReact16() {\n    if (!canUseDOM() || !isReact16) {\n      return null;\n    }\n\n    const { children } = this.props;\n\n    if (!this.node) {\n      return null;\n    }\n\n    return ReactDOM.createPortal(children, this.node);\n  }\n\n  render() {\n    if (!isReact16) {\n      return null;\n    }\n\n    return this.renderReact16();\n  }\n}\n", "import * as React from 'react';\nimport Floater, { Props as FloaterP<PERSON>, RenderProps } from 'react-floater';\nimport is from 'is-lite';\nimport treeChanges from 'tree-changes';\n\nimport { getElement, isElementVisible } from '~/modules/dom';\nimport { hideBeacon, log } from '~/modules/helpers';\nimport Scope from '~/modules/scope';\nimport { validateStep } from '~/modules/step';\n\nimport { ACTIONS, EVENTS, LIFECYCLE, STATUS } from '~/literals';\n\nimport { StepProps } from '~/types';\n\nimport Beacon from './Beacon';\nimport Tooltip from './Tooltip/index';\n\nexport default class JoyrideStep extends React.Component<StepProps> {\n  scope: Scope | null = null;\n  tooltip: HTMLElement | null = null;\n\n  componentDidMount() {\n    const { debug, index } = this.props;\n\n    log({\n      title: `step:${index}`,\n      data: [{ key: 'props', value: this.props }],\n      debug,\n    });\n  }\n\n  componentDidUpdate(previousProps: StepProps) {\n    const {\n      action,\n      callback,\n      continuous,\n      controlled,\n      debug,\n      helpers,\n      index,\n      lifecycle,\n      shouldScroll,\n      status,\n      step,\n      store,\n    } = this.props;\n    const { changed, changedFrom } = treeChanges(previousProps, this.props);\n    const state = helpers.info();\n\n    const skipBeacon =\n      continuous && action !== ACTIONS.CLOSE && (index > 0 || action === ACTIONS.PREV);\n    const hasStoreChanged =\n      changed('action') || changed('index') || changed('lifecycle') || changed('status');\n    const isInitial = changedFrom('lifecycle', [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT);\n    const isAfterAction = changed('action', [\n      ACTIONS.NEXT,\n      ACTIONS.PREV,\n      ACTIONS.SKIP,\n      ACTIONS.CLOSE,\n    ]);\n    const isControlled = controlled && index === previousProps.index;\n\n    if (isAfterAction && (isInitial || isControlled)) {\n      callback({\n        ...state,\n        index: previousProps.index,\n        lifecycle: LIFECYCLE.COMPLETE,\n        step: previousProps.step,\n        type: EVENTS.STEP_AFTER,\n      });\n    }\n\n    if (\n      step.placement === 'center' &&\n      status === STATUS.RUNNING &&\n      changed('index') &&\n      action !== ACTIONS.START &&\n      lifecycle === LIFECYCLE.INIT\n    ) {\n      store.update({ lifecycle: LIFECYCLE.READY });\n    }\n\n    if (hasStoreChanged) {\n      const element = getElement(step.target);\n      const elementExists = !!element;\n      const hasRenderedTarget = elementExists && isElementVisible(element);\n\n      if (hasRenderedTarget) {\n        if (\n          changedFrom('status', STATUS.READY, STATUS.RUNNING) ||\n          changedFrom('lifecycle', LIFECYCLE.INIT, LIFECYCLE.READY)\n        ) {\n          callback({\n            ...state,\n            step,\n            type: EVENTS.STEP_BEFORE,\n          });\n        }\n      } else {\n        // eslint-disable-next-line no-console\n        console.warn(elementExists ? 'Target not visible' : 'Target not mounted', step);\n        callback({\n          ...state,\n          type: EVENTS.TARGET_NOT_FOUND,\n          step,\n        });\n\n        if (!controlled) {\n          store.update({ index: index + (action === ACTIONS.PREV ? -1 : 1) });\n        }\n      }\n    }\n\n    if (changedFrom('lifecycle', LIFECYCLE.INIT, LIFECYCLE.READY)) {\n      store.update({\n        lifecycle: hideBeacon(step) || skipBeacon ? LIFECYCLE.TOOLTIP : LIFECYCLE.BEACON,\n      });\n    }\n\n    if (changed('index')) {\n      log({\n        title: `step:${lifecycle}`,\n        data: [{ key: 'props', value: this.props }],\n        debug,\n      });\n    }\n\n    if (changed('lifecycle', LIFECYCLE.BEACON)) {\n      callback({\n        ...state,\n        step,\n        type: EVENTS.BEACON,\n      });\n    }\n\n    if (changed('lifecycle', LIFECYCLE.TOOLTIP)) {\n      callback({\n        ...state,\n        step,\n        type: EVENTS.TOOLTIP,\n      });\n\n      if (shouldScroll && this.tooltip) {\n        this.scope = new Scope(this.tooltip, { selector: '[data-action=primary]' });\n        this.scope.setFocus();\n      }\n    }\n\n    if (changedFrom('lifecycle', [LIFECYCLE.TOOLTIP, LIFECYCLE.INIT], LIFECYCLE.INIT)) {\n      this.scope?.removeScope();\n      store.cleanupPoppers();\n    }\n  }\n\n  componentWillUnmount() {\n    this.scope?.removeScope();\n  }\n\n  /**\n   * Beacon click/hover event listener\n   */\n  handleClickHoverBeacon = (event: React.MouseEvent<HTMLElement>) => {\n    const { step, store } = this.props;\n\n    if (event.type === 'mouseenter' && step.event !== 'hover') {\n      return;\n    }\n\n    store.update({ lifecycle: LIFECYCLE.TOOLTIP });\n  };\n\n  setTooltipRef = (element: HTMLElement) => {\n    this.tooltip = element;\n  };\n\n  setPopper: FloaterProps['getPopper'] = (popper, type) => {\n    const { action, lifecycle, step, store } = this.props;\n\n    if (type === 'wrapper') {\n      store.setPopper('beacon', popper);\n    } else {\n      store.setPopper('tooltip', popper);\n    }\n\n    if (\n      store.getPopper('beacon') &&\n      (store.getPopper('tooltip') || step.placement === 'center') &&\n      lifecycle === LIFECYCLE.INIT\n    ) {\n      store.update({\n        action,\n        lifecycle: LIFECYCLE.READY,\n      });\n    }\n\n    if (step.floaterProps?.getPopper) {\n      step.floaterProps.getPopper(popper, type);\n    }\n  };\n\n  get open() {\n    const { lifecycle, step } = this.props;\n\n    return hideBeacon(step) || lifecycle === LIFECYCLE.TOOLTIP;\n  }\n\n  renderTooltip = (renderProps: RenderProps) => {\n    const { continuous, helpers, index, size, step } = this.props;\n\n    return (\n      <Tooltip\n        continuous={continuous}\n        helpers={helpers}\n        index={index}\n        isLastStep={index + 1 === size}\n        setTooltipRef={this.setTooltipRef}\n        size={size}\n        step={step}\n        {...renderProps}\n      />\n    );\n  };\n\n  render() {\n    const { continuous, debug, index, nonce, shouldScroll, size, step } = this.props;\n    const target = getElement(step.target);\n\n    if (!validateStep(step) || !is.domElement(target)) {\n      return null;\n    }\n\n    return (\n      <div key={`JoyrideStep-${index}`} className=\"react-joyride__step\">\n        <Floater\n          {...step.floaterProps}\n          component={this.renderTooltip}\n          debug={debug}\n          getPopper={this.setPopper}\n          id={`react-joyride-step-${index}`}\n          open={this.open}\n          placement={step.placement}\n          target={step.target}\n        >\n          <Beacon\n            beaconComponent={step.beaconComponent}\n            continuous={continuous}\n            index={index}\n            isLastStep={index + 1 === size}\n            locale={step.locale}\n            nonce={nonce}\n            onClickOrHover={this.handleClickHoverBeacon}\n            shouldFocus={shouldScroll}\n            size={size}\n            step={step}\n            styles={step.styles}\n          />\n        </Floater>\n      </div>\n    );\n  }\n}\n", "interface ScopeOptions {\n  code?: string;\n  selector: string | null;\n}\n\nexport default class Scope {\n  element: HTMLElement;\n  options: ScopeOptions;\n\n  constructor(element: HTMLElement, options: ScopeOptions) {\n    if (!(element instanceof HTMLElement)) {\n      throw new TypeError('Invalid parameter: element must be an HTMLElement');\n    }\n\n    this.element = element;\n    this.options = options;\n\n    window.addEventListener('keydown', this.handleKeyDown, false);\n\n    this.setFocus();\n  }\n\n  canBeTabbed = (element: HTMLElement): boolean => {\n    const { tabIndex } = element;\n\n    if (tabIndex === null || tabIndex < 0) {\n      return false;\n    }\n\n    return this.canHaveFocus(element);\n  };\n\n  canHaveFocus = (element: HTMLElement): boolean => {\n    const validTabNodes = /input|select|textarea|button|object/;\n    const nodeName = element.nodeName.toLowerCase();\n\n    const isValid =\n      (validTabNodes.test(nodeName) && !element.getAttribute('disabled')) ||\n      (nodeName === 'a' && !!element.getAttribute('href'));\n\n    return isValid && this.isVisible(element);\n  };\n\n  findValidTabElements = (): Array<HTMLElement> =>\n    [].slice.call(this.element.querySelectorAll('*'), 0).filter(this.canBeTabbed);\n\n  handleKeyDown = (event: KeyboardEvent) => {\n    const { code = 'Tab' } = this.options;\n\n    if (event.code === code) {\n      this.interceptTab(event);\n    }\n  };\n\n  interceptTab = (event: KeyboardEvent) => {\n    event.preventDefault();\n    const elements = this.findValidTabElements();\n    const { shiftKey } = event;\n\n    if (!elements.length) {\n      return;\n    }\n\n    let x = document.activeElement ? elements.indexOf(document.activeElement as HTMLElement) : 0;\n\n    if (x === -1 || (!shiftKey && x + 1 === elements.length)) {\n      x = 0;\n    } else if (shiftKey && x === 0) {\n      x = elements.length - 1;\n    } else {\n      x += shiftKey ? -1 : 1;\n    }\n\n    elements[x].focus();\n  };\n\n  // eslint-disable-next-line class-methods-use-this\n  isHidden = (element: HTMLElement) => {\n    const noSize = element.offsetWidth <= 0 && element.offsetHeight <= 0;\n    const style = window.getComputedStyle(element);\n\n    if (noSize && !element.innerHTML) {\n      return true;\n    }\n\n    return (\n      (noSize && style.getPropertyValue('overflow') !== 'visible') ||\n      style.getPropertyValue('display') === 'none'\n    );\n  };\n\n  isVisible = (element: HTMLElement): boolean => {\n    let parentElement: HTMLElement | null = element;\n\n    while (parentElement) {\n      if (parentElement instanceof HTMLElement) {\n        if (parentElement === document.body) {\n          break;\n        }\n\n        if (this.isHidden(parentElement)) {\n          return false;\n        }\n\n        parentElement = parentElement.parentNode as HTMLElement;\n      }\n    }\n\n    return true;\n  };\n\n  removeScope = () => {\n    window.removeEventListener('keydown', this.handleKeyDown);\n  };\n\n  checkFocus = (target: HTMLElement) => {\n    if (document.activeElement !== target) {\n      target.focus();\n      window.requestAnimationFrame(() => this.checkFocus(target));\n    }\n  };\n\n  setFocus = () => {\n    const { selector } = this.options;\n\n    if (!selector) {\n      return;\n    }\n\n    const target = this.element.querySelector(selector);\n\n    if (target) {\n      window.requestAnimationFrame(() => this.checkFocus(target as HTMLElement));\n    }\n  };\n}\n", "import * as React from 'react';\nimport is from 'is-lite';\n\nimport { getReactNodeText } from '~/modules/helpers';\n\nimport { BeaconProps } from '~/types';\n\nexport default class JoyrideBeacon extends React.Component<BeaconProps> {\n  private beacon: HTMLElement | null = null;\n\n  constructor(props: BeaconProps) {\n    super(props);\n\n    if (props.beaconComponent) {\n      return;\n    }\n\n    const head = document.head || document.getElementsByTagName('head')[0];\n    const style = document.createElement('style');\n\n    style.id = 'joyride-beacon-animation';\n\n    if (props.nonce) {\n      style.setAttribute('nonce', props.nonce);\n    }\n\n    const css = `\n        @keyframes joyride-beacon-inner {\n          20% {\n            opacity: 0.9;\n          }\n        \n          90% {\n            opacity: 0.7;\n          }\n        }\n        \n        @keyframes joyride-beacon-outer {\n          0% {\n            transform: scale(1);\n          }\n        \n          45% {\n            opacity: 0.7;\n            transform: scale(0.75);\n          }\n        \n          100% {\n            opacity: 0.9;\n            transform: scale(1);\n          }\n        }\n      `;\n\n    style.appendChild(document.createTextNode(css));\n\n    head.appendChild(style);\n  }\n\n  componentDidMount() {\n    const { shouldFocus } = this.props;\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!is.domElement(this.beacon)) {\n        console.warn('beacon is not a valid DOM element'); // eslint-disable-line no-console\n      }\n    }\n\n    setTimeout(() => {\n      if (is.domElement(this.beacon) && shouldFocus) {\n        this.beacon.focus();\n      }\n    }, 0);\n  }\n\n  componentWillUnmount() {\n    const style = document.getElementById('joyride-beacon-animation');\n\n    if (style?.parentNode) {\n      style.parentNode.removeChild(style);\n    }\n  }\n\n  setBeaconRef = (c: HTMLElement | null) => {\n    this.beacon = c;\n  };\n\n  render() {\n    const {\n      beaconComponent,\n      continuous,\n      index,\n      isLastStep,\n      locale,\n      onClickOrHover,\n      size,\n      step,\n      styles,\n    } = this.props;\n    const title = getReactNodeText(locale.open);\n    const sharedProps = {\n      'aria-label': title,\n      onClick: onClickOrHover,\n      onMouseEnter: onClickOrHover,\n      ref: this.setBeaconRef,\n      title,\n    };\n    let component;\n\n    if (beaconComponent) {\n      const BeaconComponent = beaconComponent;\n\n      component = (\n        <BeaconComponent\n          continuous={continuous}\n          index={index}\n          isLastStep={isLastStep}\n          size={size}\n          step={step}\n          {...sharedProps}\n        />\n      );\n    } else {\n      component = (\n        <button\n          key=\"JoyrideBeacon\"\n          className=\"react-joyride__beacon\"\n          data-test-id=\"button-beacon\"\n          style={styles.beacon}\n          type=\"button\"\n          {...sharedProps}\n        >\n          <span style={styles.beaconInner} />\n          <span style={styles.beaconOuter} />\n        </button>\n      );\n    }\n\n    return component;\n  }\n}\n", "import * as React from 'react';\n\nimport { getReactNodeText, replaceLocaleContent } from '~/modules/helpers';\n\nimport { TooltipProps } from '~/types';\n\nimport Container from './Container';\n\nexport default class JoyrideTooltip extends React.Component<TooltipProps> {\n  handleClickBack = (event: React.MouseEvent<HTMLElement>) => {\n    event.preventDefault();\n    const { helpers } = this.props;\n\n    helpers.prev();\n  };\n\n  handleClickClose = (event: React.MouseEvent<HTMLElement>) => {\n    event.preventDefault();\n    const { helpers } = this.props;\n\n    helpers.close('button_close');\n  };\n\n  handleClickPrimary = (event: React.MouseEvent<HTMLElement>) => {\n    event.preventDefault();\n    const { continuous, helpers } = this.props;\n\n    if (!continuous) {\n      helpers.close('button_primary');\n\n      return;\n    }\n\n    helpers.next();\n  };\n\n  handleClickSkip = (event: React.MouseEvent<HTMLElement>) => {\n    event.preventDefault();\n    const { helpers } = this.props;\n\n    helpers.skip();\n  };\n\n  getElementsProps = () => {\n    const { continuous, index, isLastStep, setTooltipRef, size, step } = this.props;\n    const { back, close, last, next, nextLabelWithProgress, skip } = step.locale;\n\n    const backText = getReactNodeText(back);\n    const closeText = getReactNodeText(close);\n    const lastText = getReactNodeText(last);\n    const nextText = getReactNodeText(next);\n    const skipText = getReactNodeText(skip);\n\n    let primary = close;\n    let primaryText = closeText;\n\n    if (continuous) {\n      primary = next;\n      primaryText = nextText;\n\n      if (step.showProgress && !isLastStep) {\n        const labelWithProgress = getReactNodeText(nextLabelWithProgress, {\n          step: index + 1,\n          steps: size,\n        });\n\n        primary = replaceLocaleContent(nextLabelWithProgress, index + 1, size);\n        primaryText = labelWithProgress;\n      }\n\n      if (isLastStep) {\n        primary = last;\n        primaryText = lastText;\n      }\n    }\n\n    return {\n      backProps: {\n        'aria-label': backText,\n        children: back,\n        'data-action': 'back',\n        onClick: this.handleClickBack,\n        role: 'button',\n        title: backText,\n      },\n      closeProps: {\n        'aria-label': closeText,\n        children: close,\n        'data-action': 'close',\n        onClick: this.handleClickClose,\n        role: 'button',\n        title: closeText,\n      },\n      primaryProps: {\n        'aria-label': primaryText,\n        children: primary,\n        'data-action': 'primary',\n        onClick: this.handleClickPrimary,\n        role: 'button',\n        title: primaryText,\n      },\n      skipProps: {\n        'aria-label': skipText,\n        children: skip,\n        'data-action': 'skip',\n        onClick: this.handleClickSkip,\n        role: 'button',\n        title: skipText,\n      },\n      tooltipProps: {\n        'aria-modal': true,\n        ref: setTooltipRef,\n        role: 'alertdialog',\n      },\n    };\n  };\n\n  render() {\n    const { continuous, index, isLastStep, setTooltipRef, size, step } = this.props;\n    const { beaconComponent, tooltipComponent, ...cleanStep } = step;\n    let component;\n\n    if (tooltipComponent) {\n      const renderProps = {\n        ...this.getElementsProps(),\n        continuous,\n        index,\n        isLastStep,\n        size,\n        step: cleanStep,\n        setTooltipRef,\n      };\n\n      const TooltipComponent = tooltipComponent;\n\n      component = <TooltipComponent {...renderProps} />;\n    } else {\n      component = (\n        <Container\n          {...this.getElementsProps()}\n          continuous={continuous}\n          index={index}\n          isLastStep={isLastStep}\n          size={size}\n          step={step}\n        />\n      );\n    }\n\n    return component;\n  }\n}\n", "import * as React from 'react';\n\nimport { getReactNodeText } from '~/modules/helpers';\n\nimport { TooltipRenderProps } from '~/types';\n\nimport CloseButton from './CloseButton';\n\nfunction JoyrideTooltipContainer(props: TooltipRenderProps) {\n  const { backProps, closeProps, index, isLastStep, primaryProps, skipProps, step, tooltipProps } =\n    props;\n  const { content, hideBackButton, hideClose<PERSON><PERSON>on, hideFooter, showSkipButton, styles, title } =\n    step;\n  const output: Record<string, React.ReactNode> = {};\n\n  output.primary = (\n    <button\n      data-test-id=\"button-primary\"\n      style={styles.buttonNext}\n      type=\"button\"\n      {...primaryProps}\n    />\n  );\n\n  if (showSkipButton && !isLastStep) {\n    output.skip = (\n      <button\n        aria-live=\"off\"\n        data-test-id=\"button-skip\"\n        style={styles.buttonSkip}\n        type=\"button\"\n        {...skipProps}\n      />\n    );\n  }\n\n  if (!hideBackButton && index > 0) {\n    output.back = (\n      <button data-test-id=\"button-back\" style={styles.buttonBack} type=\"button\" {...backProps} />\n    );\n  }\n\n  output.close = !hideCloseButton && (\n    <CloseButton data-test-id=\"button-close\" styles={styles.buttonClose} {...closeProps} />\n  );\n\n  return (\n    <div\n      key=\"JoyrideTooltip\"\n      aria-label={getReactNodeText(title ?? content)}\n      className=\"react-joyride__tooltip\"\n      style={styles.tooltip}\n      {...tooltipProps}\n    >\n      <div style={styles.tooltipContainer}>\n        {title && (\n          <h1 aria-label={getReactNodeText(title)} style={styles.tooltipTitle}>\n            {title}\n          </h1>\n        )}\n        <div style={styles.tooltipContent}>{content}</div>\n      </div>\n      {!hideFooter && (\n        <div style={styles.tooltipFooter}>\n          <div style={styles.tooltipFooterSpacer}>{output.skip}</div>\n          {output.back}\n          {output.primary}\n        </div>\n      )}\n      {output.close}\n    </div>\n  );\n}\n\nexport default JoyrideTooltipContainer;\n", "import React, { CSSProperties } from 'react';\n\ninterface Props {\n  styles: CSSProperties;\n}\n\nfunction JoyrideTooltipCloseButton({ styles, ...props }: Props) {\n  const { color, height, width, ...style } = styles;\n\n  return (\n    <button style={style} type=\"button\" {...props}>\n      <svg\n        height={typeof height === 'number' ? `${height}px` : height}\n        preserveAspectRatio=\"xMidYMid\"\n        version=\"1.1\"\n        viewBox=\"0 0 18 18\"\n        width={typeof width === 'number' ? `${width}px` : width}\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <g>\n          <path\n            d=\"M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.*********,0.********* C0.*********,-0.0553188426 0.*********,-0.0553188426 0.*********,0.********* C-0.0553311331,0.********* -0.0553311331,0.********* 0.*********,0.********* L8.13920499,9.00268191 L8.13911129,9.00268191 Z\"\n            fill={color}\n          />\n        </g>\n      </svg>\n    </button>\n  );\n}\n\nexport default JoyrideTooltipCloseButton;\n"], "mappings": ";;;;;AAAO,IAAM,UAAU;AAAA,EACrB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,OAAO;AAAA,EACP,MAAM;AAAA,EACN,QAAQ;AACV;AAEO,IAAM,SAAS;AAAA,EACpB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,OAAO;AACT;AAEO,IAAM,YAAY;AAAA,EACvB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AACT;AAEO,IAAM,SAAS;AAAA,EACpB,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AACX;AAEO,IAAM,SAAS;AAAA,EACpB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,OAAO;AACT;;;AClDA,YAAYA,YAAW;AAEvB,OAAO,aAAa;AACpB,OAAOC,SAAQ;AACf,OAAOC,kBAAiB;;;ACJxB,OAAO,YAAY;AACnB,OAAO,kBAAkB;AAElB,SAAS,YAAY;AAH5B;AAIE,SAAO,CAAC,EAAE,OAAO,WAAW,iBAAe,YAAO,aAAP,mBAAiB;AAC9D;AAKO,SAAS,cAAc,SAA6B;AACzD,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,sBAAsB;AACvC;AAKO,SAAS,kBAAkB,SAAS,OAAe;AACxD,QAAM,EAAE,MAAM,gBAAgB,IAAI;AAElC,MAAI,CAAC,QAAQ,CAAC,iBAAiB;AAC7B,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ;AACV,UAAM,UAAU;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,IAClB,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,CAAC;AACtB,UAAM,SAAS,KAAK,MAAM,QAAQ,SAAS,CAAC;AAE5C,QAAI,QAAQ,SAAS,MAAM,GAAG;AAC5B,cAAQ,QAAQ,SAAS,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,IACnD;AAEA,WAAO,QAAQ,MAAM;AAAA,EACvB;AAEA,SAAO,KAAK;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,EAClB;AACF;AAKO,SAAS,WAAW,SAAmD;AAC5E,MAAI,OAAO,YAAY,UAAU;AAC/B,QAAI;AACF,aAAO,SAAS,cAAc,OAAO;AAAA,IACvC,SAAS,OAAY;AACnB,UAAI,QAAQ,IAAI,aAAa,cAAc;AAEzC,gBAAQ,MAAM,KAAK;AAAA,MACrB;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAKO,SAAS,yBAAyB,IAA6C;AACpF,MAAI,CAAC,MAAM,GAAG,aAAa,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,SAAO,iBAAiB,EAAE;AAC5B;AAKO,SAAS,gBACd,SACA,SACA,aACA;AACA,MAAI,CAAC,SAAS;AACZ,WAAO,eAAe;AAAA,EACxB;AAEA,QAAM,SAAS,aAAa,OAAO;AAEnC,MAAI,QAAQ;AACV,QAAI,OAAO,WAAW,eAAe,CAAC,GAAG;AACvC,UAAI,aAAa;AACf,eAAO;AAAA,MACT;AAEA,aAAO,eAAe;AAAA,IACxB;AAEA,UAAM,eAAe,OAAO,eAAe,OAAO;AAElD,QAAI,CAAC,gBAAgB,CAAC,SAAS;AAC7B,aAAO,MAAM,WAAW;AAExB,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AAEA,SAAO;AACT;AAKO,SAAS,sBAAsB,SAA6B,SAA2B;AAC5F,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,gBAAgB,SAAS,OAAO;AAE/C,SAAO,SAAS,CAAC,OAAO,WAAW,eAAe,CAAC,IAAI;AACzD;AAKO,SAAS,sBAAsB,SAA+B;AACnE,SAAO,QAAQ,iBAAiB,SAAS;AAC3C;AAKO,SAAS,YAAY,IAA+B,OAAe,SAAkB;AAC1F,MAAI,CAAC,MAAM,EAAE,cAAc,cAAc;AACvC,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,SAAS,yBAAyB,EAAE;AAE1C,MAAI,aAAa,UAAU,aAAa,QAAQ;AAC9C,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,OAAO,aAAa,MAAM;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,GAAG,YAAY;AAClB,WAAO;AAAA,EACT;AAEA,SAAO,YAAY,GAAG,YAAY,IAAI;AACxC;AAKO,SAAS,iBAAiB,SAA8C;AAzK/E;AA0KE,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,MAAI,gBAAoC;AAExC,SAAO,eAAe;AACpB,QAAI,kBAAkB,SAAS,MAAM;AACnC;AAAA,IACF;AAEA,QAAI,yBAAyB,aAAa;AACxC,YAAM,EAAE,SAAS,WAAW,IAAI,iBAAiB,aAAa;AAE9D,UAAI,YAAY,UAAU,eAAe,UAAU;AACjD,eAAO;AAAA,MACT;AAAA,IACF;AAEA,qBAAgB,mBAAc,kBAAd,YAA+B;AAAA,EACjD;AAEA,SAAO;AACT;AAKO,SAAS,mBACd,SACA,QACA,SACQ;AA1MV;AA2ME,QAAM,cAAc,cAAc,OAAO;AACzC,QAAM,SAAS,gBAAgB,SAAS,OAAO;AAC/C,QAAM,kBAAkB,sBAAsB,SAAS,OAAO;AAC9D,QAAM,gBAAgB,YAAY,OAAO;AACzC,MAAI,YAAY;AAChB,MAAI,OAAM,gDAAa,QAAb,YAAoB;AAE9B,MAAI,mBAAmB,eAAe;AACpC,UAAM,aAAY,wCAAS,cAAT,YAAsB;AACxC,UAAM,mBAAmB,sCAAwB,cAAxB,YAAqC;AAE9D,UAAM,YAAY;AAAA,EACpB,WAAW,kBAAkB,aAAa;AACxC,gBAAY,OAAO;AAEnB,QAAI,CAAC,mBAAmB,CAAC,YAAY,OAAO,GAAG;AAC7C,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,OAAO,WAAW,eAAe,CAAC,GAAG;AACxC,aAAO,eAAe,EAAE;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO,KAAK,MAAM,MAAM,MAAM;AAChC;AAKO,SAAS,YAAY,SAA6B,QAAgB,SAA0B;AAzOnG;AA0OE,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,YAAY,GAAG,YAAY,EAAE,KAAI,kBAAa,OAAO,MAApB,YAAyB,CAAC;AACnE,MAAI,MAAM,QAAQ,sBAAsB,EAAE,MAAM;AAEhD,MAAI,CAAC,CAAC,cAAc,sBAAsB,SAAS,OAAO,KAAK,sBAAsB,OAAO,IAAI;AAC9F,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,KAAK,MAAM,MAAM,MAAM;AAEtC,SAAO,SAAS,IAAI,IAAI;AAC1B;AAEO,SAAS,iBAAwC;AA1PxD;AA2PE,UAAO,cAAS,qBAAT,YAA6B,SAAS;AAC/C;AAKO,SAAS,SACd,OACA,SACe;AACf,QAAM,EAAE,UAAU,QAAQ,IAAI;AAE9B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,EAAE,UAAU,IAAI;AAEtB,UAAM,QAAQ,QAAQ,YAAY,QAAQ,YAAY,YAAY;AAElE,WAAO,IAAI,SAAwB,OAAO,EAAE,UAAU,QAAQ,MAAM,KAAK,SAAS,GAAG,WAAS;AAC5F,UAAI,SAAS,MAAM,YAAY,6CAA6C;AAC1E,eAAO,OAAO,KAAK;AAAA,MACrB;AAEA,aAAO,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC;AACH;;;ACpRA,SAAS,cAAkB,sBAA+C;AAC1E,SAAS,oBAAoB;AAC7B,OAAO,eAAe;AACtB,OAAO,QAAQ;AAkCR,IAAM,YAAY,iBAAiB;AAKnC,SAAS,WAAW,YAAoB,UAAU,WAAmB;AAC1E,MAAI,UAAU;AAEd,MAAI,OAAO,WAAW,aAAa;AACjC,cAAU;AAAA,EACZ,WAES,SAAS,cAAc;AAC9B,cAAU;AAAA,EACZ,WAAW,OAAO,KAAK,SAAS,GAAG;AACjC,cAAU;AAAA,EACZ,WAES,QAAQ,OAAO,KAAK,KAAK,UAAU,SAAS,OAAO,GAAG;AAC7D,cAAU;AAAA,EACZ,WAES,OAAO,OAAO,mBAAmB,aAAa;AACrD,cAAU;AAAA,EACZ,WAES,OAAO,QAAQ;AACtB,cAAU;AAAA,EACZ,WAES,qDAAqD,KAAK,SAAS,GAAG;AAC7E,cAAU;AAAA,EACZ;AAEA,SAAO;AACT;AAKO,SAAS,cAAc,OAAwB;AACpD,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE,EAAE,YAAY;AACxE;AAEO,SAAS,iBAAiB,OAAkB,UAAmC,CAAC,GAAW;AAChG,QAAM,EAAE,cAAc,MAAM,MAAM,IAAI;AACtC,MAAI,OAAO,UAAU,KAAK;AAE1B,MAAI,CAAC,MAAM;AACT,QACE,eAAe,KAAK,KACpB,CAAC,OAAO,OAAO,MAAM,KAAK,EAAE,UAC5B,cAAc,MAAM,IAAI,MAAM,YAC9B;AACA,YAAM,YAAa,MAAM,KAAY,CAAC,CAAC;AAEvC,aAAO,iBAAiB,WAAW,OAAO;AAAA,IAC5C,OAAO;AACL,aAAO,UAAU,YAAY;AAAA,IAC/B;AAAA,EACF,YAAY,KAAK,SAAS,QAAQ,KAAK,KAAK,SAAS,SAAS,MAAM,QAAQ,OAAO;AACjF,WAAO,KAAK,QAAQ,UAAU,KAAK,SAAS,CAAC,EAAE,QAAQ,WAAW,MAAM,SAAS,CAAC;AAAA,EACpF;AAEA,SAAO;AACT;AAEO,SAAS,aAAa,QAAiC,MAA+B;AAC3F,MAAI,CAAC,GAAG,YAAY,MAAM,KAAK,CAAC,GAAG,MAAM,IAAI,GAAG;AAC9C,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,KAAK,MAAM,EAAE,MAAM,OAAK,KAAK,SAAS,CAAC,CAAC;AACxD;AAKO,SAAS,SAAS,KAA4B;AACnD,QAAM,iBAAiB;AACvB,QAAM,YAAY,IAAI,QAAQ,gBAAgB,CAAC,IAAI,GAAG,GAAG,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC;AAEpF,QAAM,SAAS,4CAA4C,KAAK,SAAS;AAEzE,SAAO,SAAS,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,SAAS,OAAO,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;AACjG;AAQO,SAAS,WAAW,MAAqB;AAC9C,SAAO,KAAK,iBAAiB,KAAK,cAAc;AAClD;AAOO,SAAS,WAAoB;AAClC,SAAO,CAAC,CAAC,UAAU,UAAU,WAAW,OAAO,EAAE,SAAS,WAAW,CAAC;AACxE;AAKO,SAAS,IAAI,EAAE,MAAM,QAAQ,OAAO,OAAO,OAAO,MAAM,GAAe;AAE5E,QAAM,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ;AAE7D,MAAI,OAAO;AACT,QAAI,SAAS,MAAM;AACjB,cAAQ;AAAA,QACN,oBAAoB,KAAK;AAAA,QACzB;AAAA,MACF;AAEA,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,aAAK,QAAQ,OAAK;AAChB,cAAI,GAAG,YAAY,CAAC,KAAK,EAAE,KAAK;AAC9B,kBAAM,MAAM,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;AAAA,UACvC,OAAO;AACL,kBAAM,MAAM,SAAS,CAAC,CAAC,CAAC;AAAA,UAC1B;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,MAAM,SAAS,CAAC,IAAI,CAAC;AAAA,MAC7B;AAEA,cAAQ,SAAS;AAAA,IACnB,OAAO;AACL,cAAQ,MAAM,6BAA6B;AAAA,IAC7C;AAAA,EACF;AAEF;AAKO,SAAS,OAAO;AACrB,SAAO;AACT;AAKO,SAAS,WAAgC,OAAU;AACxD,SAAO,OAAO,KAAK,KAAK;AAC1B;AAKO,SAAS,KACd,UACG,QACH;AACA,MAAI,CAAC,GAAG,YAAY,KAAK,GAAG;AAC1B,UAAM,IAAI,UAAU,oBAAoB;AAAA,EAC1C;AAEA,QAAM,SAAc,CAAC;AAErB,aAAW,OAAO,OAAO;AAEvB,QAAI,CAAC,EAAE,eAAe,KAAK,OAAO,GAAG,GAAG;AACtC,UAAI,CAAC,OAAO,SAAS,GAAmB,GAAG;AACzC,eAAO,GAAG,IAAI,MAAM,GAAG;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAKO,SAAS,KACd,UACG,QACH;AACA,MAAI,CAAC,GAAG,YAAY,KAAK,GAAG;AAC1B,UAAM,IAAI,UAAU,oBAAoB;AAAA,EAC1C;AAEA,MAAI,CAAC,OAAO,QAAQ;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,SAAc,CAAC;AAErB,aAAW,OAAO,OAAO;AAEvB,QAAI,CAAC,EAAE,eAAe,KAAK,OAAO,GAAG,GAAG;AACtC,UAAI,OAAO,SAAS,GAAmB,GAAG;AACxC,eAAO,GAAG,IAAI,MAAM,GAAG;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,qBAAqB,OAAkB,MAAc,OAA0B;AAC7F,QAAM,WAAW,CAAC,SAChB,KAAK,QAAQ,UAAU,OAAO,IAAI,CAAC,EAAE,QAAQ,WAAW,OAAO,KAAK,CAAC;AAEvE,MAAI,cAAc,KAAK,MAAM,UAAU;AACrC,WAAO,SAAS,KAAe;AAAA,EACjC;AAEA,MAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,SAAS,IAAI,MAAM;AAE3B,MAAI,cAAc,QAAQ,MAAM,YAAY,SAAS,SAAS,QAAQ,GAAG;AACvE,WAAO,aAAa,OAAuB;AAAA,MACzC,UAAU,SAAS,QAAQ;AAAA,IAC7B,CAAC;AAAA,EACH;AAEA,MAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,WAAO,aAAa,OAAuB;AAAA,MACzC,UAAU,SAAS,IAAI,CAAC,UAAqB;AAC3C,YAAI,OAAO,UAAU,UAAU;AAC7B,iBAAO,SAAS,KAAK;AAAA,QACvB;AAEA,eAAO,qBAAqB,OAAO,MAAM,KAAK;AAAA,MAChD,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,MAAI,cAAc,MAAM,IAAI,MAAM,cAAc,CAAC,OAAO,OAAO,MAAM,KAAK,EAAE,QAAQ;AAClF,UAAM,YAAa,MAAM,KAAY,CAAC,CAAC;AAEvC,WAAO,qBAAqB,WAAW,MAAM,KAAK;AAAA,EACpD;AAEA,SAAO;AACT;AAEO,SAAS,aAAa,SAAuC;AAClE,QAAM,EAAE,aAAa,WAAW,mBAAmB,mBAAmB,MAAM,OAAO,IAAI;AAEvF,SACE,CAAC,KAAK,qBACL,CAAC,eAAe,qBAAqB,cAAc,UAAU,YAC9D,KAAK,cAAc,aAClB,CAAC,KAAK,WAAW,CAAC,YAAY,MAAM;AAAA,EACrC,sBAAsB,aACrB,CAAC,UAAU,QAAQ,UAAU,OAAO,EAAuB,SAAS,SAAS;AAElF;;;ACxSA,OAAOC,gBAAe;AACtB,OAAOC,SAAQ;;;ACER,IAAM,sBAAoC;AAAA,EAC/C,SAAS;AAAA,IACP,iBAAiB;AAAA,MACf,mBAAmB;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB;AAAA,IACd,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ;AACF;AAEO,IAAM,gBAAwB;AAAA,EACnC,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,uBAAuB;AAAA,EACvB,MAAM;AAAA,EACN,MAAM;AACR;AAEO,IAAM,cAAc;AAAA,EACzB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AACpB;AAEO,IAAM,eAAe;AAAA,EAC1B,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,wBAAwB;AAAA,EACxB,YAAY,KAAK;AAAA,EACjB,gBAAgB;AAAA,EAChB,KAAK;AAAA,EACL,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,OAAO,CAAC;AACV;;;AClEA,OAAO,eAAe;AAKtB,IAAM,iBAAiB;AAAA,EACrB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AACV;AAEA,IAAM,aAAa;AAAA,EACjB,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,kBAAkB;AACpB;AAEA,IAAM,YAAY;AAAA,EAChB,cAAc;AAAA,EACd,UAAU;AACZ;AAEe,SAAR,UAA2B,OAAc,MAAkB;AAlClE;AAmCE,QAAM,EAAE,cAAc,OAAO,IAAI;AACjC,QAAM,qBAAqB,WAAU,UAAK,iBAAL,YAAqB,CAAC,GAAG,sCAAgB,CAAC,CAAC;AAChF,QAAM,eAAe,UAAU,0BAAU,CAAC,IAAG,UAAK,WAAL,YAAe,CAAC,CAAC;AAC9D,QAAM,UAAU,UAAU,gBAAgB,aAAa,WAAW,CAAC,CAAC;AACpE,QAAMC,cAAa,KAAK,cAAc,YAAY,KAAK;AACvD,MAAI,EAAE,MAAM,IAAI;AAEhB,MAAI,OAAO,aAAa,KAAK;AAC3B,YAAQ;AAAA,EACV;AAEA,MAAI,WAAW,SAAS;AACtB,YACE,OAAO,QAAQ,UAAU,YAAY,OAAO,aAAa,QAAQ,QAC7D,OAAO,aAAa,KACpB,QAAQ;AAAA,EAChB;AAEA,QAAM,UAAU;AAAA,IACd,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,KAAK;AAAA,IACL,QAAQ,QAAQ;AAAA,EAClB;AAEA,QAAM,gBAAgB;AAAA,IACpB,QAAQ;AAAA,MACN,GAAG;AAAA,MACH,SAASA,cAAa,SAAS;AAAA,MAC/B,QAAQ,QAAQ;AAAA,MAChB,UAAU;AAAA,MACV,OAAO,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,IAClB;AAAA,IACA,aAAa;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB,QAAQ;AAAA,MACzB,cAAc;AAAA,MACd,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,KAAK;AAAA,MACL,WAAW;AAAA,MACX,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,WAAW;AAAA,MACX,iBAAiB,QAAQ,SAAS,QAAQ,YAAY,EAAE,KAAK,GAAG,CAAC;AAAA,MACjE,QAAQ,aAAa,QAAQ,YAAY;AAAA,MACzC,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,MACT,UAAU;AAAA,MACV,KAAK;AAAA,MACL,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,iBAAiB,QAAQ;AAAA,MACzB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,OAAO,QAAQ;AAAA,MACf,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU;AAAA,MACV;AAAA,IACF;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,IACV;AAAA,IACA,gBAAgB;AAAA,MACd,SAAS;AAAA,IACX;AAAA,IACA,eAAe;AAAA,MACb,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,WAAW;AAAA,IACb;AAAA,IACA,qBAAqB;AAAA,MACnB,MAAM;AAAA,IACR;AAAA,IACA,YAAY;AAAA,MACV,GAAG;AAAA,MACH,iBAAiB,QAAQ;AAAA,MACzB,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,GAAG;AAAA,MACH,OAAO,QAAQ;AAAA,MACf,YAAY;AAAA,MACZ,aAAa;AAAA,IACf;AAAA,IACA,aAAa;AAAA,MACX,GAAG;AAAA,MACH,OAAO,QAAQ;AAAA,MACf,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,KAAK;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,GAAG;AAAA,MACH,OAAO,QAAQ;AAAA,MACf,UAAU;AAAA,IACZ;AAAA,IACA,SAAS;AAAA,MACP,GAAG;AAAA,MACH,iBAAiB,QAAQ;AAAA,MACzB,cAAc;AAAA,IAChB;AAAA,IACA,eAAe;AAAA,MACb,GAAG;AAAA,IACL;AAAA,IACA,qBAAqB;AAAA,MACnB,GAAG;AAAA,MACH,iBAAiB,QAAQ;AAAA,IAC3B;AAAA,IACA,WAAW;AAAA,MACT,GAAG;AAAA,MACH,iBAAiB;AAAA,IACnB;AAAA,IACA,iBAAiB;AAAA,MACf,GAAG;AAAA,MACH,WAAW,gBAAgB,QAAQ,YAAY,KAAK,QAAQ,eAAe;AAAA,IAC7E;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,QACL,QAAO,0EAAoB,WAApB,mBAA4B,UAA5B,mBAAmC,UAAnC,YAA4C,QAAQ;AAAA,MAC7D;AAAA,MACA,SAAS;AAAA,QACP,QAAQ,QAAQ,SAAS;AAAA,MAC3B;AAAA,IACF;AAAA,IACA;AAAA,EACF;AAEA,SAAO,UAAU,eAAe,YAAY;AAC9C;;;AFlLA,SAAS,aAAa,OAAc;AAClC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEO,SAAS,cAAc,OAAc,aAAgC;AAlC5E;AAmCE,QAAM,OAAO,oCAAe,CAAC;AAC7B,QAAM,aAAaC,WAAU,IAAI,CAAC,aAAa,aAAa,KAAK,GAAG,IAAI,GAAG;AAAA,IACzE,mBAAmBC,IAAG;AAAA,EACxB,CAAC;AAED,QAAM,eAAe,UAAU,OAAO,UAAU;AAChD,QAAMC,gBAAe;AAAA,IACnB,WAAW,WAAW,MAAM;AAAA,IAC5B,WAAW;AAAA,EACb;AACA,QAAM,eAAeF,WAAU,IAAI;AAAA,IACjC;AAAA,KACA,WAAM,iBAAN,YAAsB,CAAC;AAAA,KACvB,gBAAW,iBAAX,YAA2B,CAAC;AAAA,EAC9B,CAAC;AAGD,eAAa,SAAS,WAAW;AACjC,eAAa,SAASA,YAAU,kBAAa,WAAb,YAAuB,CAAC,GAAG,aAAa,aAAa;AAErF,eAAa,WAAU,iBAAM,qBAAN,YAA0B,WAAW,qBAArC,YAAyD;AAEhF,MAAI,WAAW,mBAAmB,aAAa,gBAAgB;AAC7D,iBAAa,eAAe,YAAY,WAAW;AAAA,EACrD;AAEA,MAAIE,iBAAgB,aAAa,QAAQ,iBAAiB;AACxD,iBAAa,QAAQ,gBAAgB,oBAAoB;AAAA,EAC3D;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,QAAQF,WAAU,IAAI,CAAC,gBAAe,WAAM,WAAN,YAAgB,CAAC,GAAG,WAAW,UAAU,CAAC,CAAC,CAAC;AAAA,IAClF;AAAA,IACA,QAAQ,KAAK,cAAc,eAAe;AAAA,EAC5C;AACF;AAKO,SAAS,aAAa,MAAY,QAAiB,OAAgB;AACxE,MAAI,CAACC,IAAG,YAAY,IAAI,GAAG;AACzB,QAAI;AAAA,MACF,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,KAAK,QAAQ;AAChB,QAAI;AAAA,MACF,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAKO,SAAS,cAAc,OAAoB,QAAiB,OAAgB;AACjF,MAAI,CAACA,IAAG,MAAM,KAAK,GAAG;AACpB,QAAI;AAAA,MACF,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,MAAM,OAAK,aAAa,GAAG,KAAK,CAAC;AAChD;;;AGrHA,OAAOE,SAAQ;AAYf,IAAM,eAAsB;AAAA,EAC1B,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW,UAAU;AAAA,EACrB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ,OAAO;AACjB;AACA,IAAM,YAAY,WAAW,KAAK,cAAc,cAAc,MAAM,CAAC;AAErE,IAAM,QAAN,MAAY;AAAA,EAOV,YAAY,SAAwB;AANpC,wBAAQ;AACR,wBAAQ;AACR,wBAAQ,QAAyB,oBAAI,IAAI;AACzC,wBAAQ;AACR,wBAAQ,SAA0B,oBAAI,IAAI;AAqG1C,wBAAO,eAAc,CAAC,aAAuB;AAC3C,WAAK,WAAW;AAAA,IAClB;AAEA,wBAAO,YAAW,CAAC,UAAuB;AACxC,YAAM,EAAE,MAAM,OAAO,IAAI,KAAK,SAAS;AACvC,YAAM,QAAQ;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ;AAAA,MACF;AAEA,WAAK,KAAK,IAAI,SAAS,KAAK;AAE5B,UAAI,WAAW,OAAO,WAAW,CAAC,QAAQ,MAAM,QAAQ;AACtD,cAAM,SAAS,OAAO;AAAA,MACxB;AAEA,WAAK,SAAS,KAAK;AAAA,IACrB;AAeA,wBAAO,aAAY,CAAC,SAAkD;AACpE,UAAI,SAAS,UAAU;AACrB,eAAO,KAAK;AAAA,MACd;AAEA,aAAO,KAAK;AAAA,IACd;AAEA,wBAAO,aAAY,CAAC,MAA4B,WAAuB;AACrE,UAAI,SAAS,UAAU;AACrB,aAAK,eAAe;AAAA,MACtB,OAAO;AACL,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF;AAEA,wBAAO,kBAAiB,MAAM;AAC5B,WAAK,eAAe;AACpB,WAAK,gBAAgB;AAAA,IACvB;AAEA,wBAAO,SAAQ,CAAC,SAAwB,SAAS;AAC/C,YAAM,EAAE,OAAO,OAAO,IAAI,KAAK,SAAS;AAExC,UAAI,WAAW,OAAO,SAAS;AAC7B;AAAA,MACF;AAEA,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,OAAO,OAAO,QAAQ,GAAG,OAAO,CAAC;AAAA,MAC1E,CAAC;AAAA,IACH;AAEA,wBAAO,MAAK,CAAC,cAAsB;AACjC,YAAM,EAAE,YAAY,OAAO,IAAI,KAAK,SAAS;AAE7C,UAAI,cAAc,WAAW,OAAO,SAAS;AAC3C;AAAA,MACF;AAEA,YAAM,OAAO,KAAK,SAAS,EAAE,SAAS;AAEtC,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,IAAI,OAAO,UAAU,CAAC;AAAA,QAC7D,QAAQ,OAAO,SAAS,OAAO;AAAA,MACjC,CAAC;AAAA,IACH;AAEA,wBAAO,QAAO,MAAa,KAAK,SAAS;AAEzC,wBAAO,QAAO,MAAM;AAClB,YAAM,EAAE,OAAO,OAAO,IAAI,KAAK,SAAS;AAExC,UAAI,WAAW,OAAO,SAAS;AAC7B;AAAA,MACF;AAEA,WAAK,SAAS,KAAK,aAAa,EAAE,QAAQ,QAAQ,MAAM,OAAO,QAAQ,EAAE,CAAC,CAAC;AAAA,IAC7E;AAEA,wBAAO,QAAO,MAAM;AAClB,YAAM,EAAE,OAAO,IAAI,KAAK,SAAS;AAEjC,UAAI,WAAW,OAAO,SAAS;AAC7B;AAAA,MACF;AAEA,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,QAAQ,WAAW,UAAU,QAAQ,CAAC;AAAA,MAC/E,CAAC;AAAA,IACH;AAEA,wBAAO,QAAO,MAAM;AAClB,YAAM,EAAE,OAAO,OAAO,IAAI,KAAK,SAAS;AAExC,UAAI,WAAW,OAAO,SAAS;AAC7B;AAAA,MACF;AAEA,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,MAAM,OAAO,QAAQ,EAAE,CAAC;AAAA,MACjE,CAAC;AAAA,IACH;AAEA,wBAAO,SAAQ,CAAC,UAAU,UAAU;AAClC,YAAM,EAAE,WAAW,IAAI,KAAK,SAAS;AAErC,UAAI,YAAY;AACd;AAAA,MACF;AAEA,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,OAAO,OAAO,EAAE,CAAC;AAAA,QACxD,QAAQ,UAAU,OAAO,UAAU,OAAO;AAAA,MAC5C,CAAC;AAAA,IACH;AAEA,wBAAO,QAAO,MAAM;AAClB,YAAM,EAAE,OAAO,IAAI,KAAK,SAAS;AAEjC,UAAI,WAAW,OAAO,SAAS;AAC7B;AAAA,MACF;AAEA,WAAK,SAAS;AAAA,QACZ,QAAQ,QAAQ;AAAA,QAChB,WAAW,UAAU;AAAA,QACrB,QAAQ,OAAO;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,wBAAO,SAAQ,CAAC,cAAuB;AACrC,YAAM,EAAE,OAAO,KAAK,IAAI,KAAK,SAAS;AAEtC,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK;AAAA,UACN;AAAA,YACE,QAAQ,QAAQ;AAAA,YAChB,OAAOC,IAAG,OAAO,SAAS,IAAI,YAAY;AAAA,UAC5C;AAAA,UACA;AAAA,QACF;AAAA,QACA,QAAQ,OAAO,OAAO,UAAU,OAAO;AAAA,MACzC,CAAC;AAAA,IACH;AAEA,wBAAO,QAAO,CAAC,UAAU,UAAU;AACjC,YAAM,EAAE,OAAO,OAAO,IAAI,KAAK,SAAS;AAExC,UAAK,CAAC,OAAO,UAAU,OAAO,OAAO,EAAoB,SAAS,MAAM,GAAG;AACzE;AAAA,MACF;AAEA,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK,aAAa,EAAE,QAAQ,QAAQ,MAAM,OAAO,SAAS,UAAU,IAAI,GAAG,CAAC;AAAA,QAC/E,QAAQ,OAAO;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,wBAAO,UAAS,CAAC,UAA0B;AA9S7C;AA+SI,UAAI,CAAC,aAAa,OAAO,SAAS,GAAG;AACnC,cAAM,IAAI,MAAM,mCAAmC,UAAU,KAAK,IAAI,CAAC,EAAE;AAAA,MAC3E;AAEA,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK;AAAA,UACN;AAAA,YACE,GAAG,KAAK,SAAS;AAAA,YACjB,GAAG;AAAA,YACH,SAAQ,WAAM,WAAN,YAAgB,QAAQ;AAAA,YAChC,SAAQ,WAAM,WAAN,YAAgB;AAAA,UAC1B;AAAA,UACA;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AA9RE,UAAM,EAAE,aAAa,OAAO,WAAW,QAAQ,CAAC,EAAE,IAAI,4BAAW,CAAC;AAElE,SAAK;AAAA,MACH;AAAA,QACE,QAAQ,QAAQ;AAAA,QAChB,YAAYA,IAAG,OAAO,SAAS;AAAA,QAC/B;AAAA,QACA,OAAOA,IAAG,OAAO,SAAS,IAAI,YAAY;AAAA,QAC1C,WAAW,UAAU;AAAA,QACrB,QAAQ;AAAA,QACR,QAAQ,MAAM,SAAS,OAAO,QAAQ,OAAO;AAAA,MAC/C;AAAA,MACA;AAAA,IACF;AAEA,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EAEO,WAAkB;AACvB,QAAI,CAAC,KAAK,MAAM,MAAM;AACpB,aAAO,EAAE,GAAG,aAAa;AAAA,IAC3B;AAEA,WAAO;AAAA,MACL,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK;AAAA,MACpC,YAAY,KAAK,MAAM,IAAI,YAAY,KAAK;AAAA,MAC5C,OAAO,SAAS,KAAK,MAAM,IAAI,OAAO,GAAG,EAAE;AAAA,MAC3C,WAAW,KAAK,MAAM,IAAI,WAAW,KAAK;AAAA,MAC1C,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK;AAAA,MACpC,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK;AAAA,MAChC,QAAS,KAAK,MAAM,IAAI,QAAQ,KAAgB;AAAA,IAClD;AAAA,EACF;AAAA,EAEQ,aAAa,OAAuB,QAAiB,OAAc;AArE7E;AAsEI,UAAM,EAAE,QAAQ,YAAY,OAAO,MAAM,OAAO,IAAI,KAAK,SAAS;AAClE,UAAM,WAAWA,IAAG,OAAO,MAAM,KAAK,IAAI,MAAM,QAAQ;AACxD,UAAM,YAAY,cAAc,CAAC,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,IAAI;AAErF,WAAO;AAAA,MACL,SAAQ,WAAM,WAAN,YAAgB;AAAA,MACxB;AAAA,MACA,OAAO;AAAA,MACP,YAAW,WAAM,cAAN,YAAmB,UAAU;AAAA,MACxC,SAAQ,WAAM,WAAN,YAAgB;AAAA,MACxB,OAAM,WAAM,SAAN,YAAc;AAAA,MACpB,QAAQ,cAAc,OAAO,OAAO,YAAY,WAAM,WAAN,YAAgB;AAAA,IAClE;AAAA,EACF;AAAA,EAEQ,WAAwB;AAC9B,UAAM,QAAQ,KAAK,KAAK,IAAI,OAAO;AAEnC,WAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC;AAAA,EACzC;AAAA,EAEQ,gBAAgB,UAA0B;AAChD,UAAM,SAAS,KAAK,UAAU,QAAQ;AACtC,UAAM,QAAQ,KAAK,UAAU,KAAK,SAAS,CAAC;AAE5C,WAAO,WAAW;AAAA,EACpB;AAAA,EAEQ,SAAS,WAAyC,UAAmB,OAAO;AAClF,UAAM,QAAQ,KAAK,SAAS;AAE5B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA;AAAA,IACF,IAAI;AAAA,MACF,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAEA,SAAK,MAAM,IAAI,UAAU,MAAM;AAC/B,SAAK,MAAM,IAAI,SAAS,KAAK;AAC7B,SAAK,MAAM,IAAI,aAAa,SAAS;AACrC,SAAK,MAAM,IAAI,UAAU,MAAM;AAC/B,SAAK,MAAM,IAAI,QAAQ,IAAI;AAC3B,SAAK,MAAM,IAAI,UAAU,MAAM;AAE/B,QAAI,SAAS;AACX,WAAK,MAAM,IAAI,cAAc,UAAU,UAAU;AACjD,WAAK,MAAM,IAAI,cAAc,UAAU,UAAU;AAAA,IACnD;AAEA,QAAI,KAAK,YAAY,KAAK,gBAAgB,KAAK,GAAG;AAChD,WAAK,SAAS,KAAK,SAAS,CAAC;AAAA,IAC/B;AAAA,EACF;AAAA,EAsBO,aAA2B;AAChC,WAAO;AAAA,MACL,OAAO,KAAK;AAAA,MACZ,IAAI,KAAK;AAAA,MACT,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AA8JF;AAIe,SAAR,YAA6B,SAAwB;AAC1D,SAAO,IAAI,MAAM,OAAO;AAC1B;;;ACrUA,YAAYC,YAAW;AACvB,OAAO,iBAAiB;;;ACDxB,YAAY,WAAW;AAMvB,SAAS,iBAAiB,EAAE,OAAO,GAAU;AAC3C,SACE;AAAA,IAAC;AAAA;AAAA,MACC,KAAI;AAAA,MACJ,WAAU;AAAA,MACV,gBAAa;AAAA,MACb,OAAO;AAAA;AAAA,EACT;AAEJ;AAEA,IAAO,oBAAQ;;;ADgBf,IAAqB,iBAArB,cAAkD,iBAA+B;AAAA,EAAjF;AAAA;AACE,oCAAW;AACX;AACA;AACA;AACA,iCAAQ;AAAA,MACN,aAAa;AAAA,MACb,oBAAoB;AAAA,MACpB,eAAe;AAAA,IACjB;AAgEA,yCAAgB,MAAM;AACpB,YAAM,EAAE,YAAY,gBAAgB,UAAU,IAAI,KAAK;AACvD,YAAM,mBAAmB;AAAA,QACvB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAEA,aACE,mBACC,aAAa,iBAAiB,SAAS,SAAS,IAAI,cAAc,UAAU;AAAA,IAEjF;AA+CA,2CAAkB,CAAC,UAAsB;AACvC,YAAM,EAAE,mBAAmB,IAAI,KAAK;AACpC,YAAM,EAAE,QAAQ,MAAM,UAAU,KAAK,MAAM,IAAI,KAAK;AAEpD,YAAM,UAAU,aAAa,UAAU,MAAM,UAAU,MAAM;AAC7D,YAAM,UAAU,aAAa,UAAU,MAAM,UAAU,MAAM;AAC7D,YAAM,oBAAoB,WAAW,OAAO,WAAW,MAAM;AAC7D,YAAM,mBAAmB,WAAW,QAAQ,WAAW,OAAO;AAC9D,YAAM,cAAc,oBAAoB;AAExC,UAAI,gBAAgB,oBAAoB;AACtC,aAAK,YAAY,EAAE,oBAAoB,YAAY,CAAC;AAAA,MACtD;AAAA,IACF;AAEA,wCAAe,MAAM;AACnB,YAAM,EAAE,OAAO,IAAI,KAAK;AACxB,YAAM,UAAU,WAAW,MAAM;AAEjC,UAAI,KAAK,iBAAiB,UAAU;AAClC,cAAM,EAAE,YAAY,IAAI,KAAK;AAE7B,YAAI,CAAC,aAAa;AAChB,eAAK,YAAY,EAAE,aAAa,MAAM,eAAe,MAAM,CAAC;AAAA,QAC9D;AAEA,qBAAa,KAAK,aAAa;AAE/B,aAAK,gBAAgB,OAAO,WAAW,MAAM;AAC3C,eAAK,YAAY,EAAE,aAAa,OAAO,eAAe,KAAK,CAAC;AAAA,QAC9D,GAAG,EAAE;AAAA,MACP,WAAW,YAAY,SAAS,QAAQ,GAAG;AACzC,aAAK,YAAY,CAAC,CAAC;AAAA,MACrB;AAAA,IACF;AAEA,wCAAe,MAAM;AACnB,mBAAa,KAAK,aAAa;AAE/B,WAAK,gBAAgB,OAAO,WAAW,MAAM;AAC3C,YAAI,CAAC,KAAK,UAAU;AAClB;AAAA,QACF;AAEA,aAAK,YAAY;AAAA,MACnB,GAAG,GAAG;AAAA,IACR;AAAA;AAAA,EAxKA,oBAAoB;AAClB,UAAM,EAAE,OAAO,kBAAkB,yBAAyB,OAAO,OAAO,IAAI,KAAK;AACjF,UAAM,UAAU,WAAW,MAAM;AAEjC,SAAK,eAAe,gBAAgB,4BAAW,SAAS,MAAM,wBAAwB,IAAI;AAC1F,SAAK,WAAW;AAEhB,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,UAAI,CAAC,oBAAoB,sBAAsB,SAAS,IAAI,GAAG;AAC7D,YAAI;AAAA,UACF,OAAO;AAAA,UACP,MAAM,CAAC,EAAE,KAAK,UAAU,OAAO,KAAK,aAAa,CAAC;AAAA,UAClD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,iBAAiB,UAAU,KAAK,YAAY;AAAA,EACrD;AAAA,EAEA,mBAAmB,eAA6B;AAhElD;AAiEI,UAAM,EAAE,wBAAwB,WAAW,iBAAiB,OAAO,IAAI,KAAK;AAC5E,UAAM,EAAE,QAAQ,IAAI,YAAY,eAAe,KAAK,KAAK;AAEzD,QAAI,QAAQ,QAAQ,KAAK,QAAQ,wBAAwB,GAAG;AAC1D,YAAM,UAAU,WAAW,MAAM;AAEjC,WAAK,eAAe,gBAAgB,4BAAW,SAAS,MAAM,wBAAwB,IAAI;AAAA,IAC5F;AAEA,QAAI,QAAQ,aAAa,UAAU,OAAO,GAAG;AAC3C,iBAAK,iBAAL,mBAAmB,iBAAiB,UAAU,KAAK,cAAc,EAAE,SAAS,KAAK;AAEjF,iBAAW,MAAM;AACf,cAAM,EAAE,YAAY,IAAI,KAAK;AAE7B,YAAI,CAAC,aAAa;AAChB,eAAK,YAAY,EAAE,eAAe,KAAK,CAAC;AAAA,QAC1C;AAAA,MACF,GAAG,GAAG;AAAA,IACR;AAEA,QAAI,QAAQ,iBAAiB,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,WAAW,GAAG;AACnF,UAAI,mBAAmB,cAAc,UAAU,SAAS;AACtD,eAAO,iBAAiB,aAAa,KAAK,iBAAiB,KAAK;AAAA,MAClE,WAAW,cAAc,UAAU,SAAS;AAC1C,eAAO,oBAAoB,aAAa,KAAK,eAAe;AAAA,MAC9D;AAAA,IACF;AAAA,EACF;AAAA,EAEA,uBAAuB;AA/FzB;AAgGI,SAAK,WAAW;AAEhB,WAAO,oBAAoB,aAAa,KAAK,eAAe;AAC5D,WAAO,oBAAoB,UAAU,KAAK,YAAY;AAEtD,iBAAa,KAAK,aAAa;AAC/B,iBAAa,KAAK,aAAa;AAC/B,eAAK,iBAAL,mBAAmB,oBAAoB,UAAU,KAAK;AAAA,EACxD;AAAA,EAiBA,IAAI,gBAAgB;AAClB,UAAM,EAAE,mBAAmB,IAAI,KAAK;AACpC,UAAM,EAAE,qBAAqB,WAAW,OAAO,IAAI,KAAK;AAExD,QAAI,aAAa,OAAO;AAExB,QAAI,SAAS,GAAG;AACd,mBAAa,cAAc,WAAW,OAAO,sBAAsB,OAAO;AAAA,IAC5E;AAEA,WAAO;AAAA,MACL,QAAQ,sBAAsB,YAAY;AAAA,MAC1C,QAAQ,kBAAkB;AAAA,MAC1B,eAAe,qBAAqB,SAAS;AAAA,MAC7C,GAAG;AAAA,IACL;AAAA,EACF;AAAA,EAEA,IAAI,kBAAmC;AA3IzC;AA4II,UAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,UAAM;AAAA,MACJ,yBAAyB;AAAA,MACzB;AAAA,MACA,mBAAmB;AAAA,MACnB;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM,UAAU,WAAW,MAAM;AACjC,UAAM,cAAc,cAAc,OAAO;AACzC,UAAM,gBAAgB,YAAY,OAAO;AACzC,UAAM,MAAM,mBAAmB,SAAS,kBAAkB,sBAAsB;AAEhF,WAAO;AAAA,MACL,GAAI,SAAS,IAAI,OAAO,kBAAkB,OAAO;AAAA,MACjD,QAAQ,KAAK,QAAO,gDAAa,WAAb,YAAuB,KAAK,mBAAmB,CAAC;AAAA,MACpE,MAAM,KAAK,QAAO,gDAAa,SAAb,YAAqB,KAAK,gBAAgB;AAAA,MAC5D,SAAS,gBAAgB,IAAI;AAAA,MAC7B,eAAe,kBAAkB,SAAS;AAAA,MAC1C,UAAU,gBAAgB,UAAU;AAAA,MACpC;AAAA,MACA,YAAY;AAAA,MACZ,OAAO,KAAK,QAAO,gDAAa,UAAb,YAAsB,KAAK,mBAAmB,CAAC;AAAA,IACpE;AAAA,EACF;AAAA,EAkDA,YAAY,OAAuB;AACjC,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AAEA,SAAK,SAAS,oBAAkB,EAAE,GAAG,eAAe,GAAG,MAAM,EAAE;AAAA,EACjE;AAAA,EAEA,SAAS;AACP,UAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,UAAM,EAAE,gBAAgB,UAAU,IAAI,KAAK;AAC3C,UAAM,EAAE,eAAe,eAAe,gBAAgB,IAAI;AAE1D,QAAI,cAAc,GAAG;AACnB,aAAO;AAAA,IACT;AAEA,QAAIC,aAAY,cAAc,YAAY,iBACxC,qCAAC,qBAAU,QAAQ,iBAAiB;AAItC,QAAI,WAAW,MAAM,UAAU;AAC7B,YAAM,EAAE,cAAc,QAAQ,GAAG,cAAc,IAAI;AAEnD,MAAAA,aAAY,qCAAC,SAAI,OAAO,EAAE,GAAG,cAAc,KAAIA,UAAU;AACzD,aAAO,cAAc;AAAA,IACvB;AAEA,WACE;AAAA,MAAC;AAAA;AAAA,QACC,WAAU;AAAA,QACV,gBAAa;AAAA,QACb,SAAS;AAAA,QACT,MAAK;AAAA,QACL,OAAO;AAAA;AAAA,MAENA;AAAA,IACH;AAAA,EAEJ;AACF;;;AE/PA,YAAYC,YAAW;AACvB,YAAY,cAAc;AAU1B,IAAqB,gBAArB,cAAiD,iBAAiB;AAAA,EAAlE;AAAA;AACE,gCAA2B;AAAA;AAAA,EAE3B,oBAAoB;AAClB,UAAM,EAAE,GAAG,IAAI,KAAK;AAEpB,QAAI,CAAC,UAAU,GAAG;AAChB;AAAA,IACF;AAEA,SAAK,OAAO,SAAS,cAAc,KAAK;AACxC,SAAK,KAAK,KAAK;AAEf,aAAS,KAAK,YAAY,KAAK,IAAI;AAEnC,QAAI,CAAC,WAAW;AACd,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,qBAAqB;AACnB,QAAI,CAAC,UAAU,GAAG;AAChB;AAAA,IACF;AAEA,QAAI,CAAC,WAAW;AACd,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,QAAI,CAAC,UAAU,KAAK,CAAC,KAAK,MAAM;AAC9B;AAAA,IACF;AAEA,QAAI,CAAC,WAAW;AAEd,MAAS,gCAAuB,KAAK,IAAI;AAAA,IAC3C;AAEA,QAAI,KAAK,KAAK,eAAe,SAAS,MAAM;AAC1C,eAAS,KAAK,YAAY,KAAK,IAAI;AACnC,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,QAAI,CAAC,UAAU,GAAG;AAChB;AAAA,IACF;AAEA,UAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,QAAI,KAAK,MAAM;AACb,MAAS,6CAAoC,MAAM,UAAU,KAAK,IAAI;AAAA,IACxE;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,QAAI,CAAC,UAAU,KAAK,CAAC,WAAW;AAC9B,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;AAAA,IACT;AAEA,WAAgB,sBAAa,UAAU,KAAK,IAAI;AAAA,EAClD;AAAA,EAEA,SAAS;AACP,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,cAAc;AAAA,EAC5B;AACF;;;AC1FA,YAAYC,YAAW;AACvB,OAAO,aAAqD;AAC5D,OAAOC,SAAQ;AACf,OAAOC,kBAAiB;;;ACExB,IAAqB,QAArB,MAA2B;AAAA,EAIzB,YAAY,SAAsB,SAAuB;AAHzD;AACA;AAeA,uCAAc,CAAC,YAAkC;AAC/C,YAAM,EAAE,SAAS,IAAI;AAErB,UAAI,aAAa,QAAQ,WAAW,GAAG;AACrC,eAAO;AAAA,MACT;AAEA,aAAO,KAAK,aAAa,OAAO;AAAA,IAClC;AAEA,wCAAe,CAAC,YAAkC;AAChD,YAAM,gBAAgB;AACtB,YAAM,WAAW,QAAQ,SAAS,YAAY;AAE9C,YAAM,UACH,cAAc,KAAK,QAAQ,KAAK,CAAC,QAAQ,aAAa,UAAU,KAChE,aAAa,OAAO,CAAC,CAAC,QAAQ,aAAa,MAAM;AAEpD,aAAO,WAAW,KAAK,UAAU,OAAO;AAAA,IAC1C;AAEA,gDAAuB,MACrB,CAAC,EAAE,MAAM,KAAK,KAAK,QAAQ,iBAAiB,GAAG,GAAG,CAAC,EAAE,OAAO,KAAK,WAAW;AAE9E,yCAAgB,CAAC,UAAyB;AACxC,YAAM,EAAE,OAAO,MAAM,IAAI,KAAK;AAE9B,UAAI,MAAM,SAAS,MAAM;AACvB,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF;AAEA,wCAAe,CAAC,UAAyB;AACvC,YAAM,eAAe;AACrB,YAAM,WAAW,KAAK,qBAAqB;AAC3C,YAAM,EAAE,SAAS,IAAI;AAErB,UAAI,CAAC,SAAS,QAAQ;AACpB;AAAA,MACF;AAEA,UAAI,IAAI,SAAS,gBAAgB,SAAS,QAAQ,SAAS,aAA4B,IAAI;AAE3F,UAAI,MAAM,MAAO,CAAC,YAAY,IAAI,MAAM,SAAS,QAAS;AACxD,YAAI;AAAA,MACN,WAAW,YAAY,MAAM,GAAG;AAC9B,YAAI,SAAS,SAAS;AAAA,MACxB,OAAO;AACL,aAAK,WAAW,KAAK;AAAA,MACvB;AAEA,eAAS,CAAC,EAAE,MAAM;AAAA,IACpB;AAGA;AAAA,oCAAW,CAAC,YAAyB;AACnC,YAAM,SAAS,QAAQ,eAAe,KAAK,QAAQ,gBAAgB;AACnE,YAAM,QAAQ,OAAO,iBAAiB,OAAO;AAE7C,UAAI,UAAU,CAAC,QAAQ,WAAW;AAChC,eAAO;AAAA,MACT;AAEA,aACG,UAAU,MAAM,iBAAiB,UAAU,MAAM,aAClD,MAAM,iBAAiB,SAAS,MAAM;AAAA,IAE1C;AAEA,qCAAY,CAAC,YAAkC;AAC7C,UAAI,gBAAoC;AAExC,aAAO,eAAe;AACpB,YAAI,yBAAyB,aAAa;AACxC,cAAI,kBAAkB,SAAS,MAAM;AACnC;AAAA,UACF;AAEA,cAAI,KAAK,SAAS,aAAa,GAAG;AAChC,mBAAO;AAAA,UACT;AAEA,0BAAgB,cAAc;AAAA,QAChC;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,uCAAc,MAAM;AAClB,aAAO,oBAAoB,WAAW,KAAK,aAAa;AAAA,IAC1D;AAEA,sCAAa,CAAC,WAAwB;AACpC,UAAI,SAAS,kBAAkB,QAAQ;AACrC,eAAO,MAAM;AACb,eAAO,sBAAsB,MAAM,KAAK,WAAW,MAAM,CAAC;AAAA,MAC5D;AAAA,IACF;AAEA,oCAAW,MAAM;AACf,YAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AAEA,YAAM,SAAS,KAAK,QAAQ,cAAc,QAAQ;AAElD,UAAI,QAAQ;AACV,eAAO,sBAAsB,MAAM,KAAK,WAAW,MAAqB,CAAC;AAAA,MAC3E;AAAA,IACF;AA5HE,QAAI,EAAE,mBAAmB,cAAc;AACrC,YAAM,IAAI,UAAU,mDAAmD;AAAA,IACzE;AAEA,SAAK,UAAU;AACf,SAAK,UAAU;AAEf,WAAO,iBAAiB,WAAW,KAAK,eAAe,KAAK;AAE5D,SAAK,SAAS;AAAA,EAChB;AAmHF;;;ACvIA,YAAYC,YAAW;AACvB,OAAOC,SAAQ;AAMf,IAAqB,gBAArB,cAAiD,iBAAuB;AAAA,EAGtE,YAAY,OAAoB;AAC9B,UAAM,KAAK;AAHb,wBAAQ,UAA6B;AA2ErC,wCAAe,CAAC,MAA0B;AACxC,WAAK,SAAS;AAAA,IAChB;AAxEE,QAAI,MAAM,iBAAiB;AACzB;AAAA,IACF;AAEA,UAAM,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACrE,UAAM,QAAQ,SAAS,cAAc,OAAO;AAE5C,UAAM,KAAK;AAEX,QAAI,MAAM,OAAO;AACf,YAAM,aAAa,SAAS,MAAM,KAAK;AAAA,IACzC;AAEA,UAAM,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BZ,UAAM,YAAY,SAAS,eAAe,GAAG,CAAC;AAE9C,SAAK,YAAY,KAAK;AAAA,EACxB;AAAA,EAEA,oBAAoB;AAClB,UAAM,EAAE,YAAY,IAAI,KAAK;AAE7B,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,UAAI,CAACC,IAAG,WAAW,KAAK,MAAM,GAAG;AAC/B,gBAAQ,KAAK,mCAAmC;AAAA,MAClD;AAAA,IACF;AAEA,eAAW,MAAM;AACf,UAAIA,IAAG,WAAW,KAAK,MAAM,KAAK,aAAa;AAC7C,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,IACF,GAAG,CAAC;AAAA,EACN;AAAA,EAEA,uBAAuB;AACrB,UAAM,QAAQ,SAAS,eAAe,0BAA0B;AAEhE,QAAI,+BAAO,YAAY;AACrB,YAAM,WAAW,YAAY,KAAK;AAAA,IACpC;AAAA,EACF;AAAA,EAMA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM,QAAQ,iBAAiB,OAAO,IAAI;AAC1C,UAAM,cAAc;AAAA,MAClB,cAAc;AAAA,MACd,SAAS;AAAA,MACT,cAAc;AAAA,MACd,KAAK,KAAK;AAAA,MACV;AAAA,IACF;AACA,QAAI;AAEJ,QAAI,iBAAiB;AACnB,YAAM,kBAAkB;AAExB,kBACE;AAAA,QAAC;AAAA;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACC,GAAG;AAAA;AAAA,MACN;AAAA,IAEJ,OAAO;AACL,kBACE;AAAA,QAAC;AAAA;AAAA,UACC,KAAI;AAAA,UACJ,WAAU;AAAA,UACV,gBAAa;AAAA,UACb,OAAO,OAAO;AAAA,UACd,MAAK;AAAA,UACJ,GAAG;AAAA;AAAA,QAEJ,qCAAC,UAAK,OAAO,OAAO,aAAa;AAAA,QACjC,qCAAC,UAAK,OAAO,OAAO,aAAa;AAAA,MACnC;AAAA,IAEJ;AAEA,WAAO;AAAA,EACT;AACF;;;AC5IA,YAAYC,YAAW;;;ACAvB,YAAYC,YAAW;;;ACAvB,OAAOC,YAA8B;AAMrC,SAAS,0BAA0B,EAAE,QAAQ,GAAG,MAAM,GAAU;AAC9D,QAAM,EAAE,OAAO,QAAQ,OAAO,GAAG,MAAM,IAAI;AAE3C,SACE,gBAAAA,OAAA,cAAC,YAAO,OAAc,MAAK,UAAU,GAAG,SACtC,gBAAAA,OAAA;AAAA,IAAC;AAAA;AAAA,MACC,QAAQ,OAAO,WAAW,WAAW,GAAG,MAAM,OAAO;AAAA,MACrD,qBAAoB;AAAA,MACpB,SAAQ;AAAA,MACR,SAAQ;AAAA,MACR,OAAO,OAAO,UAAU,WAAW,GAAG,KAAK,OAAO;AAAA,MAClD,OAAM;AAAA;AAAA,IAEN,gBAAAA,OAAA,cAAC,WACC,gBAAAA,OAAA;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,MAAM;AAAA;AAAA,IACR,CACF;AAAA,EACF,CACF;AAEJ;AAEA,IAAO,sBAAQ;;;ADtBf,SAAS,wBAAwB,OAA2B;AAC1D,QAAM,EAAE,WAAW,YAAY,OAAO,YAAY,cAAc,WAAW,MAAM,aAAa,IAC5F;AACF,QAAM,EAAE,SAAS,gBAAgB,iBAAiB,YAAY,gBAAgB,QAAQ,MAAM,IAC1F;AACF,QAAM,SAA0C,CAAC;AAEjD,SAAO,UACL;AAAA,IAAC;AAAA;AAAA,MACC,gBAAa;AAAA,MACb,OAAO,OAAO;AAAA,MACd,MAAK;AAAA,MACJ,GAAG;AAAA;AAAA,EACN;AAGF,MAAI,kBAAkB,CAAC,YAAY;AACjC,WAAO,OACL;AAAA,MAAC;AAAA;AAAA,QACC,aAAU;AAAA,QACV,gBAAa;AAAA,QACb,OAAO,OAAO;AAAA,QACd,MAAK;AAAA,QACJ,GAAG;AAAA;AAAA,IACN;AAAA,EAEJ;AAEA,MAAI,CAAC,kBAAkB,QAAQ,GAAG;AAChC,WAAO,OACL,qCAAC,YAAO,gBAAa,eAAc,OAAO,OAAO,YAAY,MAAK,UAAU,GAAG,WAAW;AAAA,EAE9F;AAEA,SAAO,QAAQ,CAAC,mBACd,qCAAC,uBAAY,gBAAa,gBAAe,QAAQ,OAAO,aAAc,GAAG,YAAY;AAGvF,SACE;AAAA,IAAC;AAAA;AAAA,MACC,KAAI;AAAA,MACJ,cAAY,iBAAiB,wBAAS,OAAO;AAAA,MAC7C,WAAU;AAAA,MACV,OAAO,OAAO;AAAA,MACb,GAAG;AAAA;AAAA,IAEJ,qCAAC,SAAI,OAAO,OAAO,oBAChB,SACC,qCAAC,QAAG,cAAY,iBAAiB,KAAK,GAAG,OAAO,OAAO,gBACpD,KACH,GAEF,qCAAC,SAAI,OAAO,OAAO,kBAAiB,OAAQ,CAC9C;AAAA,IACC,CAAC,cACA,qCAAC,SAAI,OAAO,OAAO,iBACjB,qCAAC,SAAI,OAAO,OAAO,uBAAsB,OAAO,IAAK,GACpD,OAAO,MACP,OAAO,OACV;AAAA,IAED,OAAO;AAAA,EACV;AAEJ;AAEA,IAAO,oBAAQ;;;ADlEf,IAAqB,iBAArB,cAAkD,iBAAwB;AAAA,EAA1E;AAAA;AACE,2CAAkB,CAAC,UAAyC;AAC1D,YAAM,eAAe;AACrB,YAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,cAAQ,KAAK;AAAA,IACf;AAEA,4CAAmB,CAAC,UAAyC;AAC3D,YAAM,eAAe;AACrB,YAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,cAAQ,MAAM,cAAc;AAAA,IAC9B;AAEA,8CAAqB,CAAC,UAAyC;AAC7D,YAAM,eAAe;AACrB,YAAM,EAAE,YAAY,QAAQ,IAAI,KAAK;AAErC,UAAI,CAAC,YAAY;AACf,gBAAQ,MAAM,gBAAgB;AAE9B;AAAA,MACF;AAEA,cAAQ,KAAK;AAAA,IACf;AAEA,2CAAkB,CAAC,UAAyC;AAC1D,YAAM,eAAe;AACrB,YAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,cAAQ,KAAK;AAAA,IACf;AAEA,4CAAmB,MAAM;AACvB,YAAM,EAAE,YAAY,OAAO,YAAY,eAAe,MAAM,KAAK,IAAI,KAAK;AAC1E,YAAM,EAAE,MAAM,OAAO,MAAM,MAAM,uBAAuB,KAAK,IAAI,KAAK;AAEtE,YAAM,WAAW,iBAAiB,IAAI;AACtC,YAAM,YAAY,iBAAiB,KAAK;AACxC,YAAM,WAAW,iBAAiB,IAAI;AACtC,YAAM,WAAW,iBAAiB,IAAI;AACtC,YAAM,WAAW,iBAAiB,IAAI;AAEtC,UAAI,UAAU;AACd,UAAI,cAAc;AAElB,UAAI,YAAY;AACd,kBAAU;AACV,sBAAc;AAEd,YAAI,KAAK,gBAAgB,CAAC,YAAY;AACpC,gBAAM,oBAAoB,iBAAiB,uBAAuB;AAAA,YAChE,MAAM,QAAQ;AAAA,YACd,OAAO;AAAA,UACT,CAAC;AAED,oBAAU,qBAAqB,uBAAuB,QAAQ,GAAG,IAAI;AACrE,wBAAc;AAAA,QAChB;AAEA,YAAI,YAAY;AACd,oBAAU;AACV,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA,aAAO;AAAA,QACL,WAAW;AAAA,UACT,cAAc;AAAA,UACd,UAAU;AAAA,UACV,eAAe;AAAA,UACf,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,cAAc;AAAA,UACd,UAAU;AAAA,UACV,eAAe;AAAA,UACf,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,cAAc;AAAA,UACd,UAAU;AAAA,UACV,eAAe;AAAA,UACf,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,cAAc;AAAA,UACd,UAAU;AAAA,UACV,eAAe;AAAA,UACf,SAAS,KAAK;AAAA,UACd,MAAM;AAAA,UACN,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,cAAc;AAAA,UACd,KAAK;AAAA,UACL,MAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA;AAAA,EAEA,SAAS;AACP,UAAM,EAAE,YAAY,OAAO,YAAY,eAAe,MAAM,KAAK,IAAI,KAAK;AAC1E,UAAM,EAAE,iBAAiB,kBAAkB,GAAG,UAAU,IAAI;AAC5D,QAAI;AAEJ,QAAI,kBAAkB;AACpB,YAAM,cAAc;AAAA,QAClB,GAAG,KAAK,iBAAiB;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN;AAAA,MACF;AAEA,YAAM,mBAAmB;AAEzB,kBAAY,qCAAC,oBAAkB,GAAG,aAAa;AAAA,IACjD,OAAO;AACL,kBACE;AAAA,QAAC;AAAA;AAAA,UACE,GAAG,KAAK,iBAAiB;AAAA,UAC1B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,MACF;AAAA,IAEJ;AAEA,WAAO;AAAA,EACT;AACF;;;AHtIA,IAAqB,cAArB,cAA+C,iBAAqB;AAAA,EAApE;AAAA;AACE,iCAAsB;AACtB,mCAA8B;AA8I9B;AAAA;AAAA;AAAA,kDAAyB,CAAC,UAAyC;AACjE,YAAM,EAAE,MAAM,MAAM,IAAI,KAAK;AAE7B,UAAI,MAAM,SAAS,gBAAgB,KAAK,UAAU,SAAS;AACzD;AAAA,MACF;AAEA,YAAM,OAAO,EAAE,WAAW,UAAU,QAAQ,CAAC;AAAA,IAC/C;AAEA,yCAAgB,CAAC,YAAyB;AACxC,WAAK,UAAU;AAAA,IACjB;AAEA,qCAAuC,CAAC,QAAQ,SAAS;AA/K3D;AAgLI,YAAM,EAAE,QAAQ,WAAW,MAAM,MAAM,IAAI,KAAK;AAEhD,UAAI,SAAS,WAAW;AACtB,cAAM,UAAU,UAAU,MAAM;AAAA,MAClC,OAAO;AACL,cAAM,UAAU,WAAW,MAAM;AAAA,MACnC;AAEA,UACE,MAAM,UAAU,QAAQ,MACvB,MAAM,UAAU,SAAS,KAAK,KAAK,cAAc,aAClD,cAAc,UAAU,MACxB;AACA,cAAM,OAAO;AAAA,UACX;AAAA,UACA,WAAW,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AAEA,WAAI,UAAK,iBAAL,mBAAmB,WAAW;AAChC,aAAK,aAAa,UAAU,QAAQ,IAAI;AAAA,MAC1C;AAAA,IACF;AAQA,yCAAgB,CAAC,gBAA6B;AAC5C,YAAM,EAAE,YAAY,SAAS,OAAO,MAAM,KAAK,IAAI,KAAK;AAExD,aACE;AAAA,QAAC;AAAA;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY,QAAQ,MAAM;AAAA,UAC1B,eAAe,KAAK;AAAA,UACpB;AAAA,UACA;AAAA,UACC,GAAG;AAAA;AAAA,MACN;AAAA,IAEJ;AAAA;AAAA,EAxMA,oBAAoB;AAClB,UAAM,EAAE,OAAO,MAAM,IAAI,KAAK;AAE9B,QAAI;AAAA,MACF,OAAO,QAAQ,KAAK;AAAA,MACpB,MAAM,CAAC,EAAE,KAAK,SAAS,OAAO,KAAK,MAAM,CAAC;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,mBAAmB,eAA0B;AA/B/C;AAgCI,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,UAAM,EAAE,SAAS,YAAY,IAAIC,aAAY,eAAe,KAAK,KAAK;AACtE,UAAM,QAAQ,QAAQ,KAAK;AAE3B,UAAM,aACJ,cAAc,WAAW,QAAQ,UAAU,QAAQ,KAAK,WAAW,QAAQ;AAC7E,UAAM,kBACJ,QAAQ,QAAQ,KAAK,QAAQ,OAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,QAAQ;AACnF,UAAM,YAAY,YAAY,aAAa,CAAC,UAAU,SAAS,UAAU,IAAI,GAAG,UAAU,IAAI;AAC9F,UAAM,gBAAgB,QAAQ,UAAU;AAAA,MACtC,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AACD,UAAM,eAAe,cAAc,UAAU,cAAc;AAE3D,QAAI,kBAAkB,aAAa,eAAe;AAChD,eAAS;AAAA,QACP,GAAG;AAAA,QACH,OAAO,cAAc;AAAA,QACrB,WAAW,UAAU;AAAA,QACrB,MAAM,cAAc;AAAA,QACpB,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AAEA,QACE,KAAK,cAAc,YACnB,WAAW,OAAO,WAClB,QAAQ,OAAO,KACf,WAAW,QAAQ,SACnB,cAAc,UAAU,MACxB;AACA,YAAM,OAAO,EAAE,WAAW,UAAU,MAAM,CAAC;AAAA,IAC7C;AAEA,QAAI,iBAAiB;AACnB,YAAM,UAAU,WAAW,KAAK,MAAM;AACtC,YAAM,gBAAgB,CAAC,CAAC;AACxB,YAAM,oBAAoB,iBAAiB,iBAAiB,OAAO;AAEnE,UAAI,mBAAmB;AACrB,YACE,YAAY,UAAU,OAAO,OAAO,OAAO,OAAO,KAClD,YAAY,aAAa,UAAU,MAAM,UAAU,KAAK,GACxD;AACA,mBAAS;AAAA,YACP,GAAG;AAAA,YACH;AAAA,YACA,MAAM,OAAO;AAAA,UACf,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AAEL,gBAAQ,KAAK,gBAAgB,uBAAuB,sBAAsB,IAAI;AAC9E,iBAAS;AAAA,UACP,GAAG;AAAA,UACH,MAAM,OAAO;AAAA,UACb;AAAA,QACF,CAAC;AAED,YAAI,CAAC,YAAY;AACf,gBAAM,OAAO,EAAE,OAAO,SAAS,WAAW,QAAQ,OAAO,KAAK,GAAG,CAAC;AAAA,QACpE;AAAA,MACF;AAAA,IACF;AAEA,QAAI,YAAY,aAAa,UAAU,MAAM,UAAU,KAAK,GAAG;AAC7D,YAAM,OAAO;AAAA,QACX,WAAW,WAAW,IAAI,KAAK,aAAa,UAAU,UAAU,UAAU;AAAA,MAC5E,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,OAAO,GAAG;AACpB,UAAI;AAAA,QACF,OAAO,QAAQ,SAAS;AAAA,QACxB,MAAM,CAAC,EAAE,KAAK,SAAS,OAAO,KAAK,MAAM,CAAC;AAAA,QAC1C;AAAA,MACF,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,aAAa,UAAU,MAAM,GAAG;AAC1C,eAAS;AAAA,QACP,GAAG;AAAA,QACH;AAAA,QACA,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,aAAa,UAAU,OAAO,GAAG;AAC3C,eAAS;AAAA,QACP,GAAG;AAAA,QACH;AAAA,QACA,MAAM,OAAO;AAAA,MACf,CAAC;AAED,UAAID,iBAAgB,KAAK,SAAS;AAChC,aAAK,QAAQ,IAAI,MAAM,KAAK,SAAS,EAAE,UAAU,wBAAwB,CAAC;AAC1E,aAAK,MAAM,SAAS;AAAA,MACtB;AAAA,IACF;AAEA,QAAI,YAAY,aAAa,CAAC,UAAU,SAAS,UAAU,IAAI,GAAG,UAAU,IAAI,GAAG;AACjF,iBAAK,UAAL,mBAAY;AACZ,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA,EAEA,uBAAuB;AA1JzB;AA2JI,eAAK,UAAL,mBAAY;AAAA,EACd;AAAA,EA4CA,IAAI,OAAO;AACT,UAAM,EAAE,WAAW,KAAK,IAAI,KAAK;AAEjC,WAAO,WAAW,IAAI,KAAK,cAAc,UAAU;AAAA,EACrD;AAAA,EAmBA,SAAS;AACP,UAAM,EAAE,YAAY,OAAO,OAAO,OAAO,cAAAA,eAAc,MAAM,KAAK,IAAI,KAAK;AAC3E,UAAM,SAAS,WAAW,KAAK,MAAM;AAErC,QAAI,CAAC,aAAa,IAAI,KAAK,CAACE,IAAG,WAAW,MAAM,GAAG;AACjD,aAAO;AAAA,IACT;AAEA,WACE,qCAAC,SAAI,KAAK,eAAe,KAAK,IAAI,WAAU,yBAC1C;AAAA,MAAC;AAAA;AAAA,QACE,GAAG,KAAK;AAAA,QACT,WAAW,KAAK;AAAA,QAChB;AAAA,QACA,WAAW,KAAK;AAAA,QAChB,IAAI,sBAAsB,KAAK;AAAA,QAC/B,MAAM,KAAK;AAAA,QACX,WAAW,KAAK;AAAA,QAChB,QAAQ,KAAK;AAAA;AAAA,MAEb;AAAA,QAAC;AAAA;AAAA,UACC,iBAAiB,KAAK;AAAA,UACtB;AAAA,UACA;AAAA,UACA,YAAY,QAAQ,MAAM;AAAA,UAC1B,QAAQ,KAAK;AAAA,UACb;AAAA,UACA,gBAAgB,KAAK;AAAA,UACrB,aAAaF;AAAA,UACb;AAAA,UACA;AAAA,UACA,QAAQ,KAAK;AAAA;AAAA,MACf;AAAA,IACF,CACF;AAAA,EAEJ;AACF;;;AVxOA,IAAM,UAAN,cAA4B,iBAAwB;AAAA,EAMlD,YAAY,OAAc;AACxB,UAAM,KAAK;AANb,wBAAiB;AACjB,wBAAiB;AAyLjB;AAAA;AAAA;AAAA,oCAAW,CAAC,SAAwB;AAClC,YAAM,EAAE,SAAS,IAAI,KAAK;AAE1B,UAAIG,IAAG,SAAS,QAAQ,GAAG;AACzB,iBAAS,IAAI;AAAA,MACf;AAAA,IACF;AAKA;AAAA;AAAA;AAAA,0CAAiB,CAAC,UAAyB;AACzC,YAAM,EAAE,OAAO,UAAU,IAAI,KAAK;AAClC,YAAM,EAAE,MAAM,IAAI,KAAK;AACvB,YAAM,OAAO,MAAM,KAAK;AAExB,UAAI,cAAc,UAAU,SAAS;AACnC,YAAI,MAAM,SAAS,YAAY,QAAQ,CAAC,KAAK,mBAAmB;AAC9D,eAAK,MAAM,MAAM,UAAU;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAEA,8CAAqB,MAAM;AACzB,YAAM,EAAE,MAAM,IAAI,KAAK;AACvB,YAAM,EAAE,MAAM,IAAI,KAAK;AAEvB,YAAM,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AAEnD,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,QAAQ,MAAM,SAAS;AAAA,MAC9B;AAAA,IACF;AAKA;AAAA;AAAA;AAAA,qCAAY,CAAC,UAAiB;AAC5B,WAAK,SAAS,KAAK;AAAA,IACrB;AAzNE,UAAM,EAAE,OAAO,YAAY,MAAM,MAAM,UAAU,IAAI;AAErD,SAAK,QAAQ,YAAY;AAAA,MACvB,GAAG;AAAA,MACH,YAAY,OAAOA,IAAG,OAAO,SAAS;AAAA,IACxC,CAAC;AACD,SAAK,UAAU,KAAK,MAAM,WAAW;AAErC,UAAM,EAAE,YAAY,IAAI,KAAK;AAE7B,QAAI;AAAA,MACF,OAAO;AAAA,MACP,MAAM;AAAA,QACJ,EAAE,KAAK,SAAS,OAAO,KAAK,MAAM;AAAA,QAClC,EAAE,KAAK,SAAS,OAAO,KAAK,MAAM;AAAA,MACpC;AAAA,MACA;AAAA,IACF,CAAC;AAGD,gBAAY,KAAK,SAAS;AAE1B,QAAI,YAAY;AACd,iBAAW,KAAK,OAAO;AAAA,IACzB;AAEA,SAAK,QAAQ,KAAK,MAAM,SAAS;AAAA,EACnC;AAAA,EAEA,oBAAoB;AAClB,QAAI,CAAC,UAAU,GAAG;AAChB;AAAA,IACF;AAEA,UAAM,EAAE,OAAO,mBAAmB,KAAK,MAAM,IAAI,KAAK;AACtD,UAAM,EAAE,MAAM,IAAI,KAAK;AAEvB,QAAI,cAAc,OAAO,KAAK,KAAK,KAAK;AACtC,YAAM;AAAA,IACR;AAEA,QAAI,CAAC,mBAAmB;AACtB,eAAS,KAAK,iBAAiB,WAAW,KAAK,gBAAgB,EAAE,SAAS,KAAK,CAAC;AAAA,IAClF;AAAA,EACF;AAAA,EAEA,mBAAmB,eAAsB,eAAsB;AAC7D,QAAI,CAAC,UAAU,GAAG;AAChB;AAAA,IACF;AAEA,UAAM,EAAE,QAAQ,YAAY,OAAO,OAAO,IAAI,KAAK;AACnD,UAAM,EAAE,OAAO,KAAK,WAAW,MAAM,IAAI,KAAK;AAC9C,UAAM,EAAE,WAAW,mBAAmB,OAAO,cAAc,IAAI;AAC/D,UAAM,EAAE,OAAO,UAAU,OAAO,MAAM,OAAO,IAAI,KAAK;AACtD,UAAM,EAAE,SAAS,aAAa,IAAIC,aAAY,eAAe,KAAK,KAAK;AACvE,UAAM,EAAE,SAAS,YAAY,IAAIA,aAAY,eAAe,KAAK,KAAK;AACtE,UAAM,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AAEnD,UAAM,eAAe,CAAC,QAAQ,eAAe,KAAK;AAClD,UAAM,mBAAmBD,IAAG,OAAO,SAAS,KAAK,aAAa,WAAW;AACzE,UAAM,SAAS,WAAW,KAAK,MAAM;AAErC,QAAI,cAAc;AAChB,UAAI,cAAc,OAAO,KAAK,GAAG;AAC/B,iBAAS,KAAK;AAAA,MAChB,OAAO;AAEL,gBAAQ,KAAK,uBAAuB,KAAK;AAAA,MAC3C;AAAA,IACF;AAEA,QAAI,aAAa,KAAK,GAAG;AACvB,UAAI,KAAK;AACP,cAAM,SAAS;AAAA,MACjB,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF;AAEA,QAAI,kBAAkB;AACpB,UAAI,aACFA,IAAG,OAAO,iBAAiB,KAAK,oBAAoB,YAAY,QAAQ,OAAO,QAAQ;AAEzF,UAAI,WAAW,QAAQ,MAAM;AAC3B,qBAAa,QAAQ;AAAA,MACvB;AAEA,UAAI,CAAE,CAAC,OAAO,UAAU,OAAO,OAAO,EAAoB,SAAS,MAAM,GAAG;AAC1E,eAAO;AAAA,UACL,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,UACnD,OAAO;AAAA,UACP,WAAW,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAGA,QAAI,CAAC,cAAc,WAAW,OAAO,WAAW,UAAU,KAAK,CAAC,QAAQ;AACtE,WAAK,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAE,CAAC;AACtC,WAAK,SAAS;AAAA,QACZ,GAAG,KAAK;AAAA,QACR,MAAM,OAAO;AAAA,QACb;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,eAAe;AAAA,MACnB,GAAG,KAAK;AAAA,MACR;AAAA,MACA;AAAA,IACF;AACA,UAAM,gBAAgB,QAAQ,UAAU;AAAA,MACtC,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AAED,QAAI,iBAAiB,QAAQ,UAAU,OAAO,MAAM,GAAG;AACrD,YAAM,eAAe,cAAc,KAAK,OAAO,MAAM,cAAc,KAAK,CAAC;AAEzE,WAAK,SAAS;AAAA,QACZ,GAAG;AAAA,QACH,OAAO,cAAc;AAAA,QACrB,WAAW,UAAU;AAAA,QACrB,MAAM;AAAA,QACN,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AAEA,QAAI,QAAQ,UAAU,CAAC,OAAO,UAAU,OAAO,OAAO,CAAC,GAAG;AACxD,YAAM,eAAe,cAAc,KAAK,OAAO,MAAM,cAAc,KAAK,CAAC;AAEzE,UAAI,CAAC,YAAY;AACf,aAAK,SAAS;AAAA,UACZ,GAAG;AAAA,UACH,OAAO,cAAc;AAAA,UACrB,WAAW,UAAU;AAAA,UACrB,MAAM;AAAA,UACN,MAAM,OAAO;AAAA,QACf,CAAC;AAAA,MACH;AAEA,WAAK,SAAS;AAAA,QACZ,GAAG;AAAA,QACH,MAAM,OAAO;AAAA;AAAA,QAEb,MAAM;AAAA,QACN,OAAO,cAAc;AAAA,MACvB,CAAC;AACD,YAAM;AAAA,IACR,WAAW,YAAY,UAAU,CAAC,OAAO,MAAM,OAAO,KAAK,GAAG,OAAO,OAAO,GAAG;AAC7E,WAAK,SAAS;AAAA,QACZ,GAAG;AAAA,QACH,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH,WAAW,QAAQ,QAAQ,KAAK,QAAQ,UAAU,QAAQ,KAAK,GAAG;AAChE,WAAK,SAAS;AAAA,QACZ,GAAG;AAAA,QACH,MAAM,OAAO;AAAA,MACf,CAAC;AAAA,IACH;AAEA,SAAK,aAAa,aAAa;AAAA,EACjC;AAAA,EAEA,uBAAuB;AACrB,UAAM,EAAE,kBAAkB,IAAI,KAAK;AAEnC,QAAI,CAAC,mBAAmB;AACtB,eAAS,KAAK,oBAAoB,WAAW,KAAK,cAAc;AAAA,IAClE;AAAA,EACF;AAAA,EA8CA,aAAa,eAAsB;AACjC,UAAM,EAAE,OAAO,WAAW,OAAO,IAAI,KAAK;AAC1C,UAAM;AAAA,MACJ;AAAA,MACA,yBAAyB;AAAA,MACzB;AAAA,MACA,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB;AAAA,IACF,IAAI,KAAK;AACT,UAAM,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AAEnD,UAAM,SAAS,WAAW,KAAK,MAAM;AACrC,UAAM,qBAAqB,aAAa;AAAA,MACtC,aAAa,UAAU;AAAA,MACvB;AAAA,MACA,mBAAmB,cAAc;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAED,QAAI,WAAW,OAAO,WAAW,oBAAoB;AACnD,YAAM,kBAAkB,sBAAsB,QAAQ,sBAAsB;AAC5E,YAAME,gBAAe,gBAAgB,QAAQ,sBAAsB;AACnE,UAAI,UAAU,KAAK,MAAM,YAAY,QAAQ,cAAc,sBAAsB,CAAC,KAAK;AAEvF,UAAI;AAAA,QACF,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,EAAE,KAAK,SAAS,OAAO,MAAM;AAAA,UAC7B,EAAE,KAAK,aAAa,OAAO,UAAU;AAAA,UACrC,EAAE,KAAK,UAAU,OAAO,OAAO;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAED,YAAM,eAAe,KAAK,MAAM,UAAU,QAAQ;AAClD,YAAM,gBAAgB,KAAK,MAAM,UAAU,SAAS;AAEpD,UAAI,cAAc,UAAU,UAAU,cAAc;AAClD,cAAM,EAAE,SAAS,UAAU,IAAI;AAE/B,YAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,SAAS,KAAK,CAAC,iBAAiB;AACvD,oBAAU,KAAK,MAAM,QAAQ,OAAO,MAAM,YAAY;AAAA,QACxD;AAAA,MACF,WAAW,cAAc,UAAU,WAAW,eAAe;AAC3D,cAAM,EAAE,SAAS,SAAS,UAAU,IAAI;AAExC,YAAI,CAAC,OAAO,SAAS,MAAM,EAAE,SAAS,SAAS,KAAK,CAAC,WAAW,CAAC,iBAAiB;AAChF,oBAAU,KAAK,MAAM,QAAQ,OAAO,MAAM,YAAY;AAAA,QACxD,OAAO;AACL,qBAAW,KAAK;AAAA,QAClB;AAAA,MACF;AAEA,gBAAU,WAAW,IAAI,UAAU;AAEnC,UAAI,WAAW,OAAO,SAAS;AAC7B,iBAAS,SAAS,EAAE,SAASA,eAAyB,UAAU,eAAe,CAAC,EAAE;AAAA,UAChF,MAAM;AACJ,uBAAW,MAAM;AA7T7B;AA8Tc,yBAAK,MAAM,UAAU,SAAS,MAA9B,mBAAiC,SAAS;AAAA,YAC5C,GAAG,EAAE;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,SAAS;AACP,QAAI,CAAC,UAAU,GAAG;AAChB,aAAO;AAAA,IACT;AAEA,UAAM,EAAE,OAAO,WAAW,OAAO,IAAI,KAAK;AAC1C,UAAM;AAAA,MACJ,aAAa;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,MACA,oBAAoB;AAAA,MACpB;AAAA,IACF,IAAI,KAAK;AACT,UAAM,YAAY,WAAW,OAAO;AACpC,UAAM,UAAqC,CAAC;AAE5C,QAAI,aAAa,MAAM,KAAK,GAAG;AAC7B,YAAM,OAAO,cAAc,KAAK,OAAO,MAAM,KAAK,CAAC;AAEnD,cAAQ,OACN;AAAA,QAAC;AAAA;AAAA,UACE,GAAG,KAAK;AAAA,UACT,UAAU,KAAK;AAAA,UACf;AAAA,UACA;AAAA,UACA,SAAS,KAAK;AAAA,UACd;AAAA,UACA,cAAc,CAAC,KAAK,qBAAqB,UAAU,KAAK;AAAA,UACxD;AAAA,UACA,OAAO,KAAK;AAAA;AAAA,MACd;AAGF,cAAQ,UACN,qCAAC,iBAAO,IAAG,0BACT;AAAA,QAAC;AAAA;AAAA,UACE,GAAG;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA,gBAAgB,KAAK;AAAA;AAAA,MACvB,CACF;AAAA,IAEJ;AAEA,WACE,qCAAC,SAAI,WAAU,mBACZ,QAAQ,MACR,QAAQ,OACX;AAAA,EAEJ;AACF;AA3VE,cAJI,SAIG,gBAAe;AA6VxB,IAAO,qBAAQ;", "names": ["React", "is", "treeChanges", "deepmerge", "is", "hideBeacon", "deepmerge", "is", "scrollParent", "is", "is", "React", "spotlight", "React", "React", "is", "treeChanges", "React", "is", "is", "React", "React", "React", "shouldScroll", "treeChanges", "is", "is", "treeChanges", "scrollParent"]}