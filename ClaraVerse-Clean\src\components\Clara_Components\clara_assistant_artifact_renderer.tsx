/**
 * Clara Assistant Artifact Renderer
 * 
 * This component handles rendering of various artifact types generated by <PERSON>,
 * including code blocks, charts, tables, HTML, markdown, and more.
 * 
 * Features:
 * - Syntax highlighting for code
 * - Interactive charts and tables
 * - HTML preview with sandboxing
 * - Copy and download functionality
 * - Theme-aware rendering
 * - Mermaid diagrams
 * - LaTeX math rendering
 * - Interactive components
 * - Enhanced artifact detection
 * - Real-time data visualization
 * - Educational tools
 */

import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { 
  Copy, 
  Check, 
  Download, 
  Eye, 
  EyeOff, 
  Code, 
  BarChart3, 
  FileText,
  Table,
  Globe,
  FileCode,
  Maximize2,
  Minimize2,
  Play,
  Square,
  Image as ImageIcon,
  Calculator,
  Palette,
  Map,
  Zap,
  BookOpen,
  Database,
  Layers,
  GitBranch,
  PieChart,
  TrendingUp,
  Calendar,
  Music,
  Video,
  Puzzle,
  Brain,
  Target,
  Lightbulb,
  Loader2,
  Edit3,
  Settings,
  RefreshCw,
  ChevronRight,
  ChevronDown,
  Filter,
  Search,
  ExternalLink,
  Paintbrush,
  Camera,
  Headphones,
  Monitor,
  Gamepad2,
  FlaskConical,
  Microscope,
  Atom,
  Binary,
  Network,
  TreePine,
  Workflow,
  AlertCircle,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Maximize,
  Move
} from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';

// Chart.js imports
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';

// Markdown import
import ReactMarkdown from 'react-markdown';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

// Import types
import { 
  ClaraArtifact, 
  ClaraArtifactRendererProps,
  ClaraArtifactType 
} from '../../types/clara_assistant_types';
import { copyToClipboard } from '../../utils/clipboard';
import { createPortal } from 'react-dom';

/**
 * Full-screen artifact modal component
 */
interface ArtifactModalProps {
  isOpen: boolean;
  onClose: () => void;
  artifact: ClaraArtifact;
  children: React.ReactNode;
}

const ArtifactModal: React.FC<ArtifactModalProps> = ({ isOpen, onClose, artifact, children }) => {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const checkTheme = () => {
      const isDarkMode = document.documentElement.classList.contains('dark') ||
                        window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDark(isDarkMode);
    };

    checkTheme();
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
    
    return () => observer.disconnect();
  }, []);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const getArtifactTypeLabel = (type: string): string => {
    const typeLabels: Record<string, string> = {
      code: 'Code',
      html: 'HTML Preview',
      markdown: 'Markdown',
      table: 'Data Table',
      chart: 'Chart',
      json: 'JSON Data',
      csv: 'CSV Data',
      diagram: 'Diagram',
      mermaid: 'Mermaid Diagram',
      'interactive-chart': 'Interactive Chart',
      'api-response': 'API Response',
      'database-result': 'Database Result'
    };
    return typeLabels[type] || type.charAt(0).toUpperCase() + type.slice(1);
  };

  const IconComponent = getArtifactIcon(artifact.type);

  // Render modal using portal to document.body to escape chat bubble constraints
  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative w-full h-full max-w-7xl max-h-[95vh] m-4 bg-white dark:bg-gray-900 rounded-xl shadow-2xl overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <IconComponent className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {artifact.title}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {getArtifactTypeLabel(artifact.type)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Copy button */}
            <button
              onClick={async () => {
                const success = await copyToClipboard(artifact.content);
                // Could add toast notification here
              }}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Copy content"
            >
              <Copy className="w-4 h-4" />
            </button>
            
            {/* Download button */}
            <button
              onClick={() => {
                const blob = new Blob([artifact.content], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `${artifact.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
                link.click();
                URL.revokeObjectURL(url);
              }}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Download content"
            >
              <Download className="w-4 h-4" />
            </button>
            
            {/* Close button */}
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Close (Esc)"
            >
              <Minimize2 className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          <div className="h-full">
            {children}
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

// Enhanced artifact types
export type EnhancedArtifactType = ClaraArtifactType | 
  'mermaid' | 'latex' | 'react-component' | 'interactive-chart' | 
  'api-response' | 'database-result' | 'form' | 'quiz' | 
  'image-gallery' | 'audio' | 'video' | 'calendar' | 
  'kanban' | 'mindmap' | 'timeline' |
  'diff' | 'sql' | 'csv-viewer' | 'json-viewer' |
  'color-palette' | 'icon-library' | 'widget' | 'game' |
  'network-graph' | 'map' | '3d-model' | 'presentation' |
  'tutorial' | 'flashcards' | 'algorithm-viz' | 'code-challenge' |
  'drawing-canvas' | 'image-editor' | 'audio-waveform' | 'rss-feed' |
  'weather-widget' | 'stock-chart' | 'social-embed' | 'pdf-viewer' |
  'spreadsheet' | 'gantt-chart' | 'org-chart' | 'decision-tree' |
  'regex-tester' | 'json-formatter' | 'base64-encoder' | 'qr-generator' |
  'markdown-editor' | 'code-diff' | 'api-docs' | 'database-schema';

/**
 * Enhanced artifact icon mapping with 25+ new types
 */
const getArtifactIcon = (type: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    // Existing types
    code: Code,
    html: Globe,
    markdown: FileText,
    table: Table,
    chart: BarChart3,
    json: FileCode,
    csv: Table,
    diagram: GitBranch,
    report: FileText,
    
    // Enhanced types
    mermaid: GitBranch,
    latex: Calculator,
    'react-component': Layers,
    'interactive-chart': TrendingUp,
    'api-response': Database,
    'database-result': Database,
    form: FileText,
    quiz: Brain,
    'image-gallery': ImageIcon,
    audio: Music,
    video: Video,
    calendar: Calendar,
    kanban: Target,
    mindmap: Lightbulb,
    timeline: TrendingUp,
    diff: Code,
    sql: Database,
    'csv-viewer': Table,
    'json-viewer': FileCode,
    'color-palette': Palette,
    'icon-library': Puzzle,
    widget: Zap,
    game: Gamepad2,
    'network-graph': Network,
    map: Map,
    '3d-model': Monitor,
    presentation: Monitor,
    tutorial: BookOpen,
    flashcards: Brain,
    'algorithm-viz': Atom,
    'code-challenge': FlaskConical,
    'drawing-canvas': Paintbrush,
    'image-editor': Camera,
    'audio-waveform': Headphones,
    'rss-feed': Globe,
    'weather-widget': TreePine,
    'stock-chart': TrendingUp,
    'social-embed': Globe,
    'pdf-viewer': FileText,
    spreadsheet: Table,
    'gantt-chart': Calendar,
    'org-chart': Network,
    'decision-tree': GitBranch,
    'regex-tester': Binary,
    'json-formatter': FileCode,
    'base64-encoder': Binary,
    'qr-generator': Zap,
    'markdown-editor': Edit3,
    'code-diff': GitBranch,
    'api-docs': Database,
    'database-schema': Database
  };
  
  return iconMap[type] || FileText;
};

/**
 * Enhanced code highlighter with execution capabilities
 */
const CodeHighlighter: React.FC<{ 
  code: string; 
  language: string; 
  isDark: boolean;
  showLineNumbers?: boolean;
  highlightLines?: number[];
  maxHeight?: number;
  isExecutable?: boolean;
  onFullScreen?: () => void;
}> = ({ 
  code, 
  language, 
  isDark, 
  showLineNumbers = true,
  highlightLines = [],
  maxHeight = 400,
  isExecutable = false,
  onFullScreen
}) => {
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<string | null>(null);
  const [showExecution, setShowExecution] = useState(false);
  
  // Mermaid preview state - will be set to true for Mermaid diagrams
  const [showMermaidPreview, setShowMermaidPreview] = useState(false);
  const [mermaidError, setMermaidError] = useState<string | null>(null);
  const [isRenderingMermaid, setIsRenderingMermaid] = useState(false);
  const [renderedSvg, setRenderedSvg] = useState<string>('');

  // Check if code is executable
  const canExecute = useMemo(() => {
    const executableLanguages = ['javascript', 'python', 'sql', 'html', 'css'];
    return isExecutable && executableLanguages.includes(language.toLowerCase());
  }, [language, isExecutable]);

  // Check if code is a Mermaid diagram
  const isMermaidDiagram = useMemo(() => {
    if (language.toLowerCase() !== 'mermaid') return false;
    
    // Check for common Mermaid diagram types
    const mermaidPatterns = [
      /^\s*graph\s+(TD|TB|BT|RL|LR)/i,
      /^\s*sequenceDiagram/i,
      /^\s*classDiagram/i,
      /^\s*stateDiagram/i,
      /^\s*erDiagram/i,
      /^\s*journey/i,
      /^\s*gantt/i,
      /^\s*pie/i,
      /^\s*flowchart/i,
      /^\s*gitGraph/i,
      /^\s*mindmap/i,
      /^\s*timeline/i,
      /^\s*quadrantChart/i,
      /^\s*requirement/i,
      /^\s*c4Context/i,
      /^\s*sankey/i
    ];
    
    return mermaidPatterns.some(pattern => pattern.test(code.trim()));
  }, [language, code]);

  // Render Mermaid diagram
  const renderMermaidPreview = useCallback(async () => {
    if (!isMermaidDiagram) return;
    
    setIsRenderingMermaid(true);
    setMermaidError(null);
    setRenderedSvg('');
    
    try {
      // Dynamic import of mermaid
      const mermaid = await import('mermaid');
      
      // Initialize mermaid with configuration
      mermaid.default.initialize({
        startOnLoad: false,
        theme: isDark ? 'dark' : 'default',
        securityLevel: 'loose',
        fontFamily: 'inherit',
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'basis'
        },
        sequence: {
          useMaxWidth: true,
          wrap: true
        },
        gantt: {
          useMaxWidth: true
        }
      });

      // Generate unique ID
      const diagramId = `mermaid-preview-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Render the diagram
      const { svg } = await mermaid.default.render(diagramId, code.trim());
      
      // Set the SVG content in React state instead of direct DOM manipulation
      setRenderedSvg(svg);
      
    } catch (error) {
      console.error('Mermaid rendering error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown rendering error';
      setMermaidError(errorMessage);
      setRenderedSvg('');
    } finally {
      setIsRenderingMermaid(false);
    }
  }, [isMermaidDiagram, code, isDark]);

  // Auto-enable preview for Mermaid diagrams on mount
  useEffect(() => {
    if (isMermaidDiagram && !showMermaidPreview) {
      setShowMermaidPreview(true);
    }
  }, [isMermaidDiagram, showMermaidPreview]);

  // Render Mermaid preview when toggled
  useEffect(() => {
    if (showMermaidPreview && isMermaidDiagram) {
      renderMermaidPreview();
    } else {
      // Clear rendered content when preview is hidden
      setRenderedSvg('');
      setMermaidError(null);
    }
  }, [showMermaidPreview, renderMermaidPreview, isMermaidDiagram]);

  // Clean up when component unmounts or code changes
  useEffect(() => {
    return () => {
      setRenderedSvg('');
      setMermaidError(null);
    };
  }, [code]);

  const executeCode = async () => {
    if (!canExecute) return;
    
    setIsExecuting(true);
    setShowExecution(true);
    
    try {
      // Implement code execution based on language
      switch (language.toLowerCase()) {
        case 'javascript':
          // Execute JavaScript in a safe context
          const result = eval(code);
          setExecutionResult(String(result));
          break;
        case 'html':
          // Render HTML in iframe
          setExecutionResult('HTML rendered successfully');
          break;
        case 'sql':
          // Mock SQL execution
          setExecutionResult('Query executed successfully (mock result)');
          break;
        default:
          setExecutionResult('Execution not implemented for this language');
      }
    } catch (error) {
      setExecutionResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <div className="relative">
      {/* Code execution and preview controls */}
      <div className="absolute top-2 right-2 z-10 flex gap-2">
        {/* Mermaid Preview Button */}
        {isMermaidDiagram && (
          <>
            <button
              onClick={() => setShowMermaidPreview(!showMermaidPreview)}
              className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white text-xs rounded-md flex items-center gap-1 transition-colors"
              title={showMermaidPreview ? "Show Mermaid code" : "Show diagram preview"}
            >
              {isRenderingMermaid ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
              {showMermaidPreview ? 'Show Code' : 'Show Preview'}
            </button>
            
            {/* Full Screen Button for Mermaid */}
            {showMermaidPreview && onFullScreen && (
              <button
                onClick={onFullScreen}
                className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md flex items-center gap-1 transition-colors"
                title="View in full screen"
              >
                <Maximize2 className="w-3 h-3" />
                Full Screen
              </button>
            )}
          </>
        )}
        
        {/* Code Execution Controls */}
        {canExecute && (
          <>
            <button
              onClick={() => setShowExecution(!showExecution)}
              className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-md flex items-center gap-1 transition-colors"
            >
              <Monitor className="w-3 h-3" />
              {showExecution ? 'Hide' : 'Show'} Output
            </button>
            <button
              onClick={executeCode}
              disabled={isExecuting}
              className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs rounded-md flex items-center gap-1 transition-colors"
            >
              {isExecuting ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <Play className="w-3 h-3" />
              )}
              Run
            </button>
          </>
        )}
      </div>
      
      {/* Show code syntax highlighter - hidden for Mermaid when preview is active */}
      {(!isMermaidDiagram || !showMermaidPreview) && (
        <SyntaxHighlighter
          language={language}
          style={isDark ? oneDark : oneLight}
          showLineNumbers={showLineNumbers}
          wrapLines={true}
          lineProps={(lineNumber) => ({
            style: {
              backgroundColor: highlightLines.includes(lineNumber) 
                ? (isDark ? 'rgba(255, 255, 0, 0.1)' : 'rgba(255, 255, 0, 0.2)')
                : undefined
            }
          })}
          customStyle={{
            margin: 0,
            maxHeight: maxHeight,
            overflow: 'auto'
          }}
        >
            {code}
        </SyntaxHighlighter>
      )}
      
      {/* Mermaid Preview - shown by default for Mermaid diagrams */}
      {isMermaidDiagram && showMermaidPreview && (
        <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
            <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Mermaid Diagram Preview
          </div>
          
          <div className="min-h-[100px] flex items-center justify-center bg-white dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-600 overflow-auto"
               style={{ maxHeight: '500px' }}>
            {isRenderingMermaid && (
              <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Rendering diagram...</span>
              </div>
            )}
            
            {!isRenderingMermaid && mermaidError && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg max-w-md">
                <div className="text-red-700 dark:text-red-400 font-medium text-sm mb-2">
                  ❌ Mermaid Rendering Failed
                </div>
                <div className="text-red-600 dark:text-red-400 text-xs mb-2">
                  {mermaidError}
                </div>
                <div className="text-red-500 dark:text-red-400 text-xs">
                  Please check your diagram syntax and try again.
                </div>
              </div>
            )}
            
            {!isRenderingMermaid && !mermaidError && renderedSvg && (
              <div 
                className="w-full flex justify-center"
                dangerouslySetInnerHTML={{ __html: renderedSvg }}
                style={{
                  maxWidth: '100%',
                  overflow: 'auto'
                }}
              />
            )}
            
            {!isRenderingMermaid && !mermaidError && !renderedSvg && (
              <div className="text-gray-500 dark:text-gray-400 text-sm">
                No diagram to display
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Code Execution Result */}
      {showExecution && executionResult && (
        <div className="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg border">
          <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 flex items-center gap-2">
            <Monitor className="w-3 h-3" />
            Output:
          </div>
          <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
            {executionResult}
          </pre>
        </div>
      )}
    </div>
  );
};

/**
 * Interactive table renderer with advanced features
 */
const TableRenderer: React.FC<{ content: string; onFullScreen?: () => void }> = ({ content, onFullScreen }) => {
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filterText, setFilterText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const itemsPerPage = 10;

  const tableData = useMemo(() => {
    try {
      const parsed = JSON.parse(content);
      return Array.isArray(parsed) ? parsed : [parsed];
    } catch {
      // Try to parse as CSV
      const lines = content.split('\n').filter(line => line.trim());
      if (lines.length === 0) return [];
      
      const headers = lines[0].split(',').map(h => h.trim());
      return lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.trim());
        const obj: any = {};
        headers.forEach((header, index) => {
          obj[header] = values[index] || '';
        });
        return obj;
      });
    }
  }, [content]);

  const columns = useMemo(() => {
    if (tableData.length === 0) return [];
    return Object.keys(tableData[0]);
  }, [tableData]);

  const filteredData = useMemo(() => {
    if (!filterText) return tableData;
    return tableData.filter(row =>
      Object.values(row).some(value =>
        String(value).toLowerCase().includes(filterText.toLowerCase())
      )
    );
  }, [tableData, filterText]);

  const sortedData = useMemo(() => {
    if (!sortColumn) return filteredData;
    
    return [...filteredData].sort((a, b) => {
      const aVal = a[sortColumn];
      const bVal = b[sortColumn];
      
      if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
      if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [filteredData, sortColumn, sortDirection]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedData, currentPage]);

  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
      } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const handleRowSelect = (index: number) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedRows(newSelected);
  };

  const exportData = (format: 'csv' | 'json') => {
    const dataToExport = selectedRows.size > 0 
      ? sortedData.filter((_, index) => selectedRows.has(index))
      : sortedData;

    if (format === 'csv') {
      const csv = [
        columns.join(','),
        ...dataToExport.map(row => columns.map(col => `"${row[col]}"`).join(','))
      ].join('\n');
      
      const blob = new Blob([csv], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'table-data.csv';
      link.click();
      URL.revokeObjectURL(url);
    } else {
      const json = JSON.stringify(dataToExport, null, 2);
      const blob = new Blob([json], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'table-data.json';
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  if (tableData.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        No data to display
      </div>
    );
  }

    return (
    <div className="space-y-4">
      {/* Table controls */}
      <div className="flex items-center justify-between flex-wrap gap-2">
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search data..."
              value={filterText}
              onChange={(e) => setFilterText(e.target.value)}
              className="pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            />
      </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center gap-2"
          >
            <Filter className="w-4 h-4" />
            Filters
          </button>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {sortedData.length} rows {selectedRows.size > 0 && `(${selectedRows.size} selected)`}
          </span>
          {onFullScreen && (
            <button
              onClick={onFullScreen}
              className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors flex items-center gap-1"
              title="View in full screen"
            >
              <Maximize2 className="w-4 h-4" />
              Full Screen
            </button>
          )}
          <button
            onClick={() => exportData('csv')}
            className="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors"
          >
            Export CSV
          </button>
          <button
            onClick={() => exportData('json')}
            className="px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors"
          >
            Export JSON
          </button>
        </div>
      </div>

      {/* Advanced filters */}
      {showFilters && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
          <div className="text-sm font-medium mb-2">Advanced Filters</div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {columns.slice(0, 3).map(column => (
              <div key={column}>
                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                  {column}
                </label>
                <input
                  type="text"
                  placeholder={`Filter by ${column}...`}
                  className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto border border-gray-300 dark:border-gray-600 rounded-lg">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-800">
              <th className="border-b border-gray-300 dark:border-gray-600 px-4 py-2 w-8">
                <input
                  type="checkbox"
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedRows(new Set(Array.from({ length: sortedData.length }, (_, i) => i)));
                    } else {
                      setSelectedRows(new Set());
                    }
                  }}
                  checked={selectedRows.size === sortedData.length && sortedData.length > 0}
                />
              </th>
              {columns.map((column) => (
                <th
                  key={column}
                  onClick={() => handleSort(column)}
                  className="border-b border-gray-300 dark:border-gray-600 px-4 py-2 text-left cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center gap-2">
                    {column}
                    {sortColumn === column && (
                      <span className="text-xs">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
              </th>
            ))}
          </tr>
        </thead>
          <tbody>
            {paginatedData.map((row, index) => {
              const actualIndex = (currentPage - 1) * itemsPerPage + index;
              return (
                <tr 
                  key={actualIndex} 
                  className={`hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${
                    selectedRows.has(actualIndex) ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <td className="border-b border-gray-200 dark:border-gray-700 px-4 py-2">
                    <input
                      type="checkbox"
                      checked={selectedRows.has(actualIndex)}
                      onChange={() => handleRowSelect(actualIndex)}
                    />
                  </td>
                  {columns.map((column) => (
                    <td
                      key={column}
                      className="border-b border-gray-200 dark:border-gray-700 px-4 py-2 text-gray-900 dark:text-gray-100"
                    >
                      {String(row[column])}
                </td>
              ))}
            </tr>
              );
            })}
        </tbody>
      </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md disabled:opacity-50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            Previous
          </button>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md disabled:opacity-50 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
};

/**
 * Mermaid diagram renderer with enhanced error handling and debugging
 */
const MermaidRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [syntaxErrors, setSyntaxErrors] = useState<string[]>([]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const diagramRef = useRef<HTMLDivElement>(null);
  const diagramId = useRef(`mermaid-diagram-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  
  // Zoom and pan state
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Enhanced syntax validation for Mermaid diagrams
  const validateMermaidSyntax = (content: string): { isValid: boolean; errors: string[]; suggestions: string[] } => {
    const errors: string[] = [];
    const suggestions: string[] = [];
    const lines = content.trim().split('\n');
    
    if (lines.length === 0 || !content.trim()) {
      errors.push('Empty diagram content');
      suggestions.push('Add a diagram type like "graph TD" or "sequenceDiagram"');
      return { isValid: false, errors, suggestions };
    }

    const firstLine = lines[0].trim().toLowerCase();
    
    // Check for valid diagram types
    const validTypes = [
      'graph', 'flowchart', 'sequencediagram', 'classDiagram', 'stateDiagram',
      'erDiagram', 'journey', 'gantt', 'pie', 'gitgraph', 'mindmap', 'timeline',
      'requirement', 'c4context'
    ];
    
    const hasValidType = validTypes.some(type => firstLine.startsWith(type.toLowerCase()));
    if (!hasValidType) {
      errors.push(`Invalid or missing diagram type. First line: "${lines[0]}"`);
      suggestions.push('Start with a valid diagram type: graph TD, sequenceDiagram, classDiagram, etc.');
    }

    // Check for common syntax issues
    lines.forEach((line, index) => {
      const trimmed = line.trim();
      if (!trimmed) return;

      // Check for unmatched brackets
      const openBrackets = (trimmed.match(/\[/g) || []).length;
      const closeBrackets = (trimmed.match(/\]/g) || []).length;
      if (openBrackets !== closeBrackets) {
        errors.push(`Line ${index + 1}: Unmatched brackets - ${openBrackets} open, ${closeBrackets} close`);
        suggestions.push(`Check line ${index + 1} for proper bracket matching: [Node Text]`);
      }

      // Check for unmatched parentheses
      const openParens = (trimmed.match(/\(/g) || []).length;
      const closeParens = (trimmed.match(/\)/g) || []).length;
      if (openParens !== closeParens) {
        errors.push(`Line ${index + 1}: Unmatched parentheses - ${openParens} open, ${closeParens} close`);
        suggestions.push(`Check line ${index + 1} for proper parentheses matching: (Node Text)`);
      }

      // Check for invalid characters in node IDs
      const nodeIdMatch = trimmed.match(/^(\w+)\s*[\[\(]/);
      if (nodeIdMatch && /[^a-zA-Z0-9_]/.test(nodeIdMatch[1])) {
        errors.push(`Line ${index + 1}: Invalid characters in node ID "${nodeIdMatch[1]}"`);
        suggestions.push(`Use only letters, numbers, and underscores in node IDs`);
      }
    });

    // Check for subgraph issues
    const subgraphLines = lines.filter(line => line.trim().toLowerCase().startsWith('subgraph'));
    const endLines = lines.filter(line => line.trim().toLowerCase() === 'end');
    if (subgraphLines.length !== endLines.length) {
      errors.push(`Subgraph mismatch: ${subgraphLines.length} subgraph(s), ${endLines.length} end(s)`);
      suggestions.push('Each "subgraph" must have a corresponding "end"');
    }

    return { 
      isValid: errors.length === 0, 
      errors, 
      suggestions: suggestions.slice(0, 5) // Limit suggestions
    };
  };

  // Zoom and pan handlers for regular renderer
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(5, zoom * delta));
    setZoom(newZoom);
  }, [zoom]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 0) { // Left mouse button
      setIsDragging(true);
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
      e.preventDefault();
    }
  }, [pan]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Zoom control functions for regular renderer
  const zoomIn = useCallback(() => {
    setZoom(prev => Math.min(5, prev * 1.2));
  }, []);

  const zoomOut = useCallback(() => {
    setZoom(prev => Math.max(0.1, prev / 1.2));
  }, []);

  const resetZoom = useCallback(() => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  }, []);

  const fitToScreen = useCallback(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      const containerRect = container.getBoundingClientRect();
      const svgElement = container.querySelector('svg');
      
      if (svgElement) {
        const svgRect = svgElement.getBoundingClientRect();
        const scaleX = (containerRect.width - 40) / svgRect.width;
        const scaleY = (containerRect.height - 40) / svgRect.height;
        const newZoom = Math.min(scaleX, scaleY, 1);
        
        setZoom(newZoom);
        setPan({ x: 0, y: 0 });
      }
    }
  }, []);

  useEffect(() => {
    const renderMermaid = async () => {
      try {
        setIsLoading(true);
        setError(null);
        setSyntaxErrors([]);
        setDebugInfo(null);
        
        console.log('🎨 Starting Mermaid diagram rendering...');
        console.log('📝 Diagram content:', content);
        
        // Pre-validate syntax
        const validation = validateMermaidSyntax(content);
        if (!validation.isValid) {
          setSyntaxErrors(validation.errors);
          throw new Error(`Syntax validation failed: ${validation.errors.join(', ')}`);
        }
        
        // Dynamic import of mermaid
        console.log('📦 Importing Mermaid library...');
        const mermaid = await import('mermaid');
        console.log('✅ Mermaid library imported successfully');
        
        // Initialize mermaid with comprehensive configuration
        const config = {
          startOnLoad: false,
          theme: 'default' as const,
          securityLevel: 'loose' as const,
          fontFamily: 'inherit',
          logLevel: 'debug' as const, // Enable debug logging
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis' as const,
            padding: 20
          },
          sequence: {
            useMaxWidth: true,
            wrap: true,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35
          },
          gantt: {
            useMaxWidth: true,
            leftPadding: 75,
            gridLineStartPadding: 35,
            fontSize: 11,
            sectionFontSize: 11,
            numberSectionStyles: 4
          },
          class: {
            useMaxWidth: true
          },
          state: {
            useMaxWidth: true
          },
          er: {
            useMaxWidth: true
          },
          pie: {
            useMaxWidth: true
          }
        };
        
        console.log('⚙️ Initializing Mermaid with config:', config);
        mermaid.default.initialize(config);

        if (diagramRef.current) {
          // Clear previous content
          diagramRef.current.innerHTML = '';
          
          // Clean and validate the diagram content
          const cleanContent = content.trim();
          
          if (!cleanContent) {
            throw new Error('Empty diagram content after cleaning');
          }
          
          console.log('🧹 Cleaned diagram content:', cleanContent);
          
          // Generate unique ID for this diagram
          const uniqueId = diagramId.current;
          console.log('🆔 Using diagram ID:', uniqueId);
          
          // Attempt to parse the diagram first (this will catch syntax errors)
          console.log('🔍 Validating diagram syntax with Mermaid parser...');
          
          try {
            // Try to render the diagram
            console.log('🎨 Rendering diagram with Mermaid...');
            const renderResult = await mermaid.default.render(uniqueId, cleanContent);
            console.log('✅ Mermaid render result:', renderResult);
            console.log('📏 SVG length:', renderResult.svg?.length || 0);
            console.log('🔍 SVG preview:', renderResult.svg?.substring(0, 200) + '...');
            
            if (!renderResult.svg || renderResult.svg.length === 0) {
              throw new Error('Mermaid returned empty SVG');
            }
            
            // Insert the SVG into the container
            diagramRef.current.innerHTML = renderResult.svg;
            console.log('📦 SVG inserted into container');
            
            // Make the SVG responsive and properly styled
            const svgElement = diagramRef.current.querySelector('svg');
            if (svgElement) {
              console.log('🎨 Found SVG element, applying styles...');
              
              // Remove any existing width/height attributes that might constrain the SVG
              svgElement.removeAttribute('width');
              svgElement.removeAttribute('height');
              
              // Apply responsive styling
              svgElement.style.width = '100%';
              svgElement.style.height = 'auto';
              svgElement.style.maxWidth = '100%';
              svgElement.style.display = 'block';
              svgElement.style.margin = '0 auto';
              svgElement.style.background = 'transparent';
              svgElement.style.border = '1px solid #e5e7eb'; // Debug border
              svgElement.style.borderRadius = '8px';
              svgElement.style.padding = '16px';
              svgElement.style.minHeight = '200px'; // Ensure minimum height
              
              // Force visibility
              svgElement.style.visibility = 'visible';
              svgElement.style.opacity = '1';
              
              console.log('✅ SVG styling applied successfully');
              console.log('📏 SVG final dimensions:', {
                computedWidth: window.getComputedStyle(svgElement).width,
                computedHeight: window.getComputedStyle(svgElement).height,
                viewBox: svgElement.getAttribute('viewBox'),
                clientWidth: svgElement.clientWidth,
                clientHeight: svgElement.clientHeight,
                boundingRect: svgElement.getBoundingClientRect()
              });
              
              // Check if SVG has any visible content
              const svgContent = svgElement.innerHTML;
              console.log('📝 SVG content length:', svgContent.length);
              console.log('🔍 SVG content preview:', svgContent.substring(0, 300) + '...');
              
              // Force a repaint
              svgElement.style.transform = 'translateZ(0)';
              
            } else {
              console.error('❌ No SVG element found in rendered content');
              console.log('🔍 Container innerHTML:', diagramRef.current.innerHTML.substring(0, 500));
            }
            
            // Also ensure the container is visible
            if (diagramRef.current) {
              diagramRef.current.style.display = 'block';
              diagramRef.current.style.visibility = 'visible';
              diagramRef.current.style.opacity = '1';
              diagramRef.current.style.minHeight = '200px';
              diagramRef.current.style.border = '2px solid #3b82f6'; // Debug border for container
              diagramRef.current.style.borderRadius = '8px';
              diagramRef.current.style.padding = '8px';
              diagramRef.current.style.backgroundColor = '#f9fafb';
              
              console.log('📦 Container styling applied');
              console.log('📏 Container dimensions:', {
                clientWidth: diagramRef.current.clientWidth,
                clientHeight: diagramRef.current.clientHeight,
                offsetWidth: diagramRef.current.offsetWidth,
                offsetHeight: diagramRef.current.offsetHeight
              });
            }
            
            setDebugInfo({
              diagramId: uniqueId,
              contentLength: cleanContent.length,
              lineCount: cleanContent.split('\n').length,
              renderTime: Date.now(),
              svgGenerated: !!renderResult.svg,
              svgLength: renderResult.svg?.length || 0,
              svgElementFound: !!svgElement,
              containerVisible: diagramRef.current?.style.visibility !== 'hidden'
            });
            
            console.log('✅ Mermaid diagram rendered successfully');
            
            // Add a small delay and then log final status
            setTimeout(() => {
              if (diagramRef.current && svgElement) {
                console.log('🔍 Final render status check:');
                console.log('- Container visible:', diagramRef.current.offsetHeight > 0);
                console.log('- SVG visible:', svgElement.getBoundingClientRect().height > 0);
                console.log('- SVG computed style:', window.getComputedStyle(svgElement).display);
              }
            }, 100);
            
          } catch (renderError) {
            console.error('❌ Mermaid render error:', renderError);
            
            // Enhanced error analysis
            const errorMessage = renderError instanceof Error ? renderError.message : String(renderError);
            let enhancedError = errorMessage;
            let specificSuggestions: string[] = [];
            
            if (errorMessage.includes('Parse error')) {
              enhancedError = 'Diagram syntax error - the diagram structure is invalid';
              specificSuggestions.push('Check for typos in node names and connections');
              specificSuggestions.push('Ensure all arrows and connections use valid syntax');
            } else if (errorMessage.includes('Lexical error')) {
              enhancedError = 'Invalid characters or tokens in the diagram';
              specificSuggestions.push('Check for special characters that might not be allowed');
              specificSuggestions.push('Ensure node IDs contain only letters, numbers, and underscores');
            } else if (errorMessage.includes('subgraph')) {
              enhancedError = 'Subgraph syntax error';
              specificSuggestions.push('Each "subgraph" must have a corresponding "end"');
              specificSuggestions.push('Check subgraph nesting and syntax');
            }
            
            setDebugInfo({
              diagramId: uniqueId,
              contentLength: cleanContent.length,
              lineCount: cleanContent.split('\n').length,
              errorType: 'render_error',
              originalError: errorMessage,
              enhancedError,
              specificSuggestions
            });
            
            throw new Error(enhancedError);
          }
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error('❌ Complete Mermaid rendering failure:', err);
        const errorMessage = err instanceof Error ? err.message : 'Unknown rendering error';
        setError(errorMessage);
        setIsLoading(false);
        
        // Show the raw content as fallback with enhanced debugging
        if (diagramRef.current) {
          diagramRef.current.innerHTML = `
            <div style="padding: 16px; background: #fee2e2; border: 1px solid #fca5a5; border-radius: 8px; margin: 8px 0;">
              <div style="font-weight: 600; color: #dc2626; margin-bottom: 8px;">❌ Mermaid Rendering Failed</div>
              <div style="font-size: 14px; color: #7f1d1d; margin-bottom: 12px;">Error: ${errorMessage}</div>
              <div style="font-size: 12px; font-weight: 500; color: #374151; margin-bottom: 8px;">📋 Diagram Source (${content.split('\n').length} lines):</div>
              <pre style="font-size: 11px; color: #6b7280; white-space: pre-wrap; font-family: monospace; overflow-x: auto; max-height: 160px; background: #f9fafb; padding: 8px; border-radius: 4px; border: 1px solid #e5e7eb;">${content}</pre>
            </div>
          `;
        }
      }
    };

    renderMermaid();
  }, [content]);

  if (isLoading) {
  return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Rendering Mermaid diagram...</span>
      </div>
    );
  }

  if (error || syntaxErrors.length > 0) {
    const validation = validateMermaidSyntax(content);
    
    return (
      <div className="space-y-4">
        {/* Main Error Display */}
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-start space-x-2">
            <svg className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <div className="flex-1">
              <div className="text-red-700 dark:text-red-400 font-medium">
                ❌ Mermaid Diagram Rendering Failed
              </div>
              <div className="text-red-600 dark:text-red-400 text-sm mt-1">
                {error || 'Syntax validation errors detected'}
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Error Analysis */}
        {(syntaxErrors.length > 0 || validation.errors.length > 0) && (
          <div className="p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
            <div className="text-orange-700 dark:text-orange-400 font-medium text-sm mb-2">
              🔍 Syntax Issues Detected:
            </div>
            <ul className="text-orange-600 dark:text-orange-400 text-xs space-y-1">
              {(syntaxErrors.length > 0 ? syntaxErrors : validation.errors).map((err, index) => (
                <li key={index} className="flex items-start space-x-1">
                  <span className="text-orange-500">•</span>
                  <span>{err}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Suggestions */}
        {validation.suggestions.length > 0 && (
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="text-blue-700 dark:text-blue-400 font-medium text-sm mb-2">
              💡 Suggestions to Fix:
            </div>
            <ul className="text-blue-600 dark:text-blue-400 text-xs space-y-1">
              {validation.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start space-x-1">
                  <span className="text-blue-500">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Debug Information */}
        {debugInfo && (
          <div className="p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">🔧 Debug Information:</div>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div>• Diagram ID: {debugInfo.diagramId}</div>
              <div>• Content Length: {debugInfo.contentLength} characters</div>
              <div>• Line Count: {debugInfo.lineCount} lines</div>
              {debugInfo.errorType && <div>• Error Type: {debugInfo.errorType}</div>}
              {debugInfo.originalError && <div>• Original Error: {debugInfo.originalError}</div>}
              {debugInfo.specificSuggestions && (
                <div>• Specific Suggestions: {debugInfo.specificSuggestions.join(', ')}</div>
              )}
            </div>
          </div>
        )}
        
        {/* Show raw diagram content for debugging */}
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            📋 Diagram Source ({content.split('\n').length} lines):
          </div>
          <pre className="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap font-mono overflow-x-auto max-h-60 border border-gray-200 dark:border-gray-600 rounded p-2 bg-white dark:bg-gray-900">
            {content}
          </pre>
        </div>
        
        {/* Common Mermaid Examples */}
        <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="text-green-700 dark:text-green-400 font-medium text-sm mb-2">
            ✅ Common Mermaid Diagram Examples:
          </div>
          <div className="text-green-600 dark:text-green-400 text-xs space-y-2">
            <div>
              <strong>Flowchart:</strong>
              <pre className="mt-1 p-2 bg-green-100 dark:bg-green-900/40 rounded text-xs">{`graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]`}</pre>
            </div>
            <div>
              <strong>Sequence Diagram:</strong>
              <pre className="mt-1 p-2 bg-green-100 dark:bg-green-900/40 rounded text-xs">{`sequenceDiagram
    Alice->>Bob: Hello Bob
    Bob-->>Alice: Hello Alice`}</pre>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${isFullscreen ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900 p-8' : ''}`}>
      {/* Controls Bar */}
      <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-t-lg">
        {/* Zoom Controls */}
        <div className="flex items-center space-x-2">
          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
            {Math.round(zoom * 100)}%
          </span>
          <button
            onClick={zoomOut}
            className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Zoom Out"
          >
            <ZoomOut className="w-3 h-3" />
          </button>
          <button
            onClick={resetZoom}
            className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Reset Zoom"
          >
            <RotateCcw className="w-3 h-3" />
          </button>
          <button
            onClick={zoomIn}
            className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Zoom In"
          >
            <ZoomIn className="w-3 h-3" />
          </button>
          <button
            onClick={fitToScreen}
            className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title="Fit to Screen"
          >
            <Maximize className="w-3 h-3" />
          </button>
        </div>

        {/* Info and Fullscreen */}
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {isDragging ? <Move className="w-3 h-3 inline" /> : 'Scroll zoom, drag pan'}
          </span>
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
            title={isFullscreen ? "Exit fullscreen" : "View fullscreen"}
          >
            {isFullscreen ? (
              <Minimize2 className="w-3 h-3" />
            ) : (
              <Maximize2 className="w-3 h-3" />
            )}
          </button>
        </div>
      </div>
      
      {/* Interactive Diagram Container */}
      <div 
        ref={containerRef}
        className={`relative overflow-hidden bg-white dark:bg-gray-900 border-x border-b border-gray-200 dark:border-gray-700 ${isFullscreen ? 'h-full rounded-b-lg' : 'min-h-[200px] max-h-[600px] rounded-b-lg'}`}
        style={{ 
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div 
          ref={diagramRef}
          className="w-full h-full flex justify-center items-center transition-transform duration-150 ease-out p-4"
          style={{ 
            transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
            transformOrigin: 'center center'
          }}
        />
        
        {/* Zoom level indicator */}
        {zoom !== 1 && (
          <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
            {Math.round(zoom * 100)}%
          </div>
        )}
      </div>
      
      {/* Success info */}
      {!isFullscreen && debugInfo && !error && (
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-center space-x-4">
          <span>✅ Rendered successfully</span>
          <span>📏 {debugInfo.lineCount} lines</span>
          <span>🆔 {debugInfo.diagramId}</span>
        </div>
      )}
    </div>
  );
};

/**
 * Interactive chart renderer with multiple chart types using Chart.js
 */
const InteractiveChartRenderer: React.FC<{ content: string; artifact: ClaraArtifact; onFullScreen?: () => void }> = ({ content, artifact, onFullScreen }) => {
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie' | 'doughnut'>('bar');
  const [showData, setShowData] = useState(false);
  
  const chartData = useMemo(() => {
    try {
      const parsed = JSON.parse(content);
      
      // Ensure proper Chart.js format
      if (!parsed.labels || !parsed.datasets) {
        // Convert simple data to Chart.js format
        if (Array.isArray(parsed)) {
          return {
            labels: parsed.map((_, i) => `Item ${i + 1}`),
            datasets: [{
              label: 'Data',
              data: parsed,
              backgroundColor: [
                'rgba(255, 99, 132, 0.2)',
                'rgba(54, 162, 235, 0.2)',
                'rgba(255, 205, 86, 0.2)',
                'rgba(75, 192, 192, 0.2)',
                'rgba(153, 102, 255, 0.2)',
                'rgba(255, 159, 64, 0.2)',
              ],
              borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 205, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)',
              ],
              borderWidth: 1,
            }]
          };
        }
      }
      
      return parsed;
    } catch {
      return {
        labels: ['Sample A', 'Sample B', 'Sample C', 'Sample D'],
        datasets: [{
          label: 'Sample Data',
          data: [12, 19, 3, 5],
          backgroundColor: [
            'rgba(255, 99, 132, 0.2)',
            'rgba(54, 162, 235, 0.2)',
            'rgba(255, 205, 86, 0.2)',
            'rgba(75, 192, 192, 0.2)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)',
          ],
          borderWidth: 1,
        }]
      };
    }
  }, [content]);

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: artifact.title || 'Interactive Chart',
      },
    },
    scales: chartType === 'pie' || chartType === 'doughnut' ? {} : {
      y: {
        beginAtZero: true,
      },
    },
  };

  const chartTypes = [
    { value: 'bar', label: 'Bar Chart', icon: BarChart3 },
    { value: 'line', label: 'Line Chart', icon: TrendingUp },
    { value: 'pie', label: 'Pie Chart', icon: PieChart },
    { value: 'doughnut', label: 'Doughnut', icon: Target }
  ];

  const renderChart = () => {
    switch (chartType) {
      case 'line':
        return <Line data={chartData} options={chartOptions} />;
      case 'pie':
        return <Pie data={chartData} options={chartOptions} />;
      case 'doughnut':
        return <Doughnut data={chartData} options={chartOptions} />;
      default:
        return <Bar data={chartData} options={chartOptions} />;
    }
  };

  return (
    <div className="space-y-4">
      {/* Chart controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {chartTypes.map(({ value, label, icon: Icon }) => (
        <button
              key={value}
              onClick={() => setChartType(value as any)}
              className={`px-3 py-2 rounded-md text-sm flex items-center gap-2 transition-colors ${
                chartType === value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
            >
              <Icon className="w-4 h-4" />
              {label}
        </button>
          ))}
        </div>
        
        <div className="flex items-center gap-2">
          {onFullScreen && (
            <button
              onClick={onFullScreen}
              className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors flex items-center gap-1"
              title="View in full screen"
            >
              <Maximize2 className="w-4 h-4" />
              Full Screen
            </button>
          )}
          <button
            onClick={() => setShowData(!showData)}
            className="px-3 py-2 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md text-sm transition-colors flex items-center gap-2"
          >
            <Database className="w-4 h-4" />
            {showData ? 'Hide' : 'Show'} Data
          </button>
        </div>
      </div>

      {/* Chart rendering */}
      <div className="h-64 bg-white dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        {renderChart()}
      </div>

      {/* Raw data display */}
      {showData && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-sm font-medium mb-2">Chart Data</div>
          <pre className="text-xs text-gray-600 dark:text-gray-400 overflow-x-auto">
            {JSON.stringify(chartData, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

/**
 * HTML renderer with sandboxed preview
 */
const HtmlRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [showPreview, setShowPreview] = useState(true);
  
  return (
    <div className="space-y-4">
      {/* HTML controls */}
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium">HTML Preview</div>
        <button
          onClick={() => setShowPreview(!showPreview)}
          className="px-3 py-2 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md text-sm transition-colors flex items-center gap-2"
        >
          <Globe className="w-4 h-4" />
          {showPreview ? 'Show Code' : 'Show Preview'}
        </button>
      </div>

      {/* HTML content */}
      {showPreview ? (
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
          <iframe
            srcDoc={content}
            className="w-full h-64 bg-white"
            sandbox="allow-scripts allow-same-origin"
            title="HTML Preview"
          />
        </div>
      ) : (
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
          <SyntaxHighlighter
            language="html"
            style={oneDark}
            customStyle={{ margin: 0 }}
          >
        {content}
          </SyntaxHighlighter>
        </div>
      )}
    </div>
  );
};

/**
 * Markdown renderer with preview using ReactMarkdown
 */
const MarkdownRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [showPreview, setShowPreview] = useState(true);
  
  return (
    <div className="space-y-4">
      {/* Markdown controls */}
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium">Markdown</div>
        <button
          onClick={() => setShowPreview(!showPreview)}
          className="px-3 py-2 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md text-sm transition-colors flex items-center gap-2"
        >
          <FileText className="w-4 h-4" />
          {showPreview ? 'Show Source' : 'Show Preview'}
        </button>
      </div>

      {/* Markdown content */}
      {showPreview ? (
        <div className="prose dark:prose-invert max-w-none p-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg">
          <ReactMarkdown>{content}</ReactMarkdown>
        </div>
      ) : (
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
          <SyntaxHighlighter
            language="markdown"
            style={oneDark}
            customStyle={{ margin: 0 }}
          >
          {content}
          </SyntaxHighlighter>
        </div>
      )}
    </div>
  );
};

/**
 * Full-screen optimized Mermaid renderer for modal display
 */
const FullScreenMermaidRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [renderedSvg, setRenderedSvg] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDark, setIsDark] = useState(false);
  
  // Zoom and pan state
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Theme detection
  useEffect(() => {
    const checkTheme = () => {
      const isDarkMode = document.documentElement.classList.contains('dark') ||
                        window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDark(isDarkMode);
    };

    checkTheme();
    const observer = new MutationObserver(checkTheme);
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
    
    return () => observer.disconnect();
  }, []);

  // Render Mermaid diagram optimized for full-screen
  const renderMermaidFullScreen = useCallback(async () => {
    if (!content.trim()) {
      setError('No diagram content provided');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Dynamic import of mermaid
      const mermaid = await import('mermaid');
      
      // Configure mermaid for full-screen display
      mermaid.default.initialize({
        startOnLoad: false,
        theme: isDark ? 'dark' : 'default',
        securityLevel: 'loose',
        fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
        fontSize: 16,
        // Full-screen optimized settings
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'basis'
        },
        sequence: {
          useMaxWidth: true,
          wrap: true
        },
        gantt: {
          useMaxWidth: true
        },
        journey: {
          useMaxWidth: true
        },
        timeline: {
          useMaxWidth: true
        }
      });

      // Generate unique ID for this render
      const uniqueId = `fullscreen-mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Render the diagram
      const result = await mermaid.default.render(uniqueId, content.trim());
      
      // Set the rendered SVG with full-screen optimizations
      let optimizedSvg = result.svg;
      
      // Remove fixed width/height and make it responsive
      optimizedSvg = optimizedSvg
        .replace(/width="[^"]*"/g, 'width="100%"')
        .replace(/height="[^"]*"/g, 'height="100%"')
        .replace(/style="[^"]*"/g, 'style="max-width: 100%; height: auto;"');
      
      setRenderedSvg(optimizedSvg);
      setError(null);
      
    } catch (err) {
      console.error('Full-screen Mermaid rendering error:', err);
      setError(err instanceof Error ? err.message : 'Failed to render diagram');
    } finally {
      setIsLoading(false);
    }
  }, [content, isDark]);

  // Render when content or theme changes
  useEffect(() => {
    renderMermaidFullScreen();
  }, [renderMermaidFullScreen]);

  // Zoom and pan handlers
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    const newZoom = Math.max(0.1, Math.min(5, zoom * delta));
    setZoom(newZoom);
  }, [zoom]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 0) { // Left mouse button
      setIsDragging(true);
      setDragStart({ x: e.clientX - pan.x, y: e.clientY - pan.y });
      e.preventDefault();
    }
  }, [pan]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      setPan({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  }, [isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Zoom control functions
  const zoomIn = useCallback(() => {
    setZoom(prev => Math.min(5, prev * 1.2));
  }, []);

  const zoomOut = useCallback(() => {
    setZoom(prev => Math.max(0.1, prev / 1.2));
  }, []);

  const resetZoom = useCallback(() => {
    setZoom(1);
    setPan({ x: 0, y: 0 });
  }, []);

  const fitToScreen = useCallback(() => {
    if (containerRef.current) {
      const container = containerRef.current;
      const containerRect = container.getBoundingClientRect();
      const svgElement = container.querySelector('svg');
      
      if (svgElement) {
        const svgRect = svgElement.getBoundingClientRect();
        const scaleX = (containerRect.width - 40) / svgRect.width;
        const scaleY = (containerRect.height - 40) / svgRect.height;
        const newZoom = Math.min(scaleX, scaleY, 1);
        
        setZoom(newZoom);
        setPan({ x: 0, y: 0 });
      }
    }
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Rendering full-screen diagram...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full min-h-[400px] p-8">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Diagram Rendering Error
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={renderMermaidFullScreen}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!renderedSvg) {
    return (
      <div className="flex items-center justify-center h-full min-h-[400px]">
        <p className="text-gray-500 dark:text-gray-400">No diagram to display</p>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col">
      {/* Zoom Controls */}
      <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Zoom: {Math.round(zoom * 100)}%
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400">
            {isDragging ? <Move className="w-4 h-4 inline" /> : 'Scroll to zoom, drag to pan'}
          </span>
        </div>
        
        <div className="flex items-center space-x-1">
          <button
            onClick={zoomOut}
            className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Zoom Out"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
          
          <button
            onClick={resetZoom}
            className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Reset Zoom"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          
          <button
            onClick={zoomIn}
            className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Zoom In"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          
          <button
            onClick={fitToScreen}
            className="p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Fit to Screen"
          >
            <Maximize className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Interactive Diagram Container */}
      <div 
        ref={containerRef}
        className="flex-1 overflow-hidden relative bg-white dark:bg-gray-900"
        style={{ 
          minHeight: '400px',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div 
          className="w-full h-full flex items-center justify-center transition-transform duration-150 ease-out"
          style={{ 
            transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
            transformOrigin: 'center center'
          }}
          dangerouslySetInnerHTML={{ __html: renderedSvg }}
        />
        
        {/* Zoom level indicator */}
        {zoom !== 1 && (
          <div className="absolute top-4 left-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-sm">
            {Math.round(zoom * 100)}%
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * React component preview renderer
 */
const ReactComponentRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [showCode, setShowCode] = useState(false);
  const [error, setError] = useState<string | null>(null);

  return (
    <div className="space-y-4">
      {/* Component controls */}
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium">React Component Preview</div>
        <button
          onClick={() => setShowCode(!showCode)}
          className="px-3 py-2 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md text-sm transition-colors flex items-center gap-2"
        >
          <Code className="w-4 h-4" />
          {showCode ? 'Hide' : 'Show'} Code
        </button>
      </div>

      {/* Component preview */}
      <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 bg-white dark:bg-gray-900">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <Layers className="w-12 h-12 mx-auto mb-2" />
          <div>React Component Preview</div>
          <div className="text-sm">Component would render here with live preview</div>
        </div>
      </div>

      {/* Component code */}
      {showCode && (
        <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
          <SyntaxHighlighter
            language="jsx"
            style={oneDark}
            customStyle={{ margin: 0 }}
          >
            {content}
          </SyntaxHighlighter>
        </div>
      )}

      {error && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="text-red-700 dark:text-red-400 text-sm">
            {error}
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Main Clara Artifact Renderer Component
 */
const ClaraArtifactRenderer: React.FC<ClaraArtifactRendererProps> = ({
  artifact,
  isExpanded = false,
  onToggleExpanded,
  onCopy,
  onDownload
}) => {
  const [copied, setCopied] = useState(false);
  const [internalExpanded, setInternalExpanded] = useState(isExpanded);
  const [isDark, setIsDark] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Detect theme (basic implementation - can be enhanced with proper theme context)
  useEffect(() => {
    const checkTheme = () => {
      setIsDark(
        document.documentElement.classList.contains('dark') ||
        window.matchMedia('(prefers-color-scheme: dark)').matches
      );
    };
    
    checkTheme();
    window.addEventListener('storage', checkTheme);
    return () => window.removeEventListener('storage', checkTheme);
  }, []);

  const IconComponent = getArtifactIcon(artifact.type);
  const expanded = onToggleExpanded ? isExpanded : internalExpanded;

  const handleToggleExpanded = () => {
    if (onToggleExpanded) {
      onToggleExpanded(artifact.id);
    } else {
      setInternalExpanded(!internalExpanded);
    }
  };

  const handleCopy = async () => {
    const success = await copyToClipboard(artifact.content);
    if (success) {
      setCopied(true);
      onCopy?.(artifact.content);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleDownload = () => {
    try {
      const blob = new Blob([artifact.content], { 
        type: getContentType(artifact.type) 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${artifact.title}.${getFileExtension(artifact.type)}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      onDownload?.(artifact);
    } catch (error) {
      console.error('Failed to download artifact:', error);
    }
  };

  const getContentType = (type: ClaraArtifactType): string => {
    const types = {
      code: 'text/plain',
      html: 'text/html',
      markdown: 'text/markdown',
      csv: 'text/csv',
      json: 'application/json',
      table: 'application/json',
      chart: 'application/json',
      mermaid: 'text/plain',
      diagram: 'text/plain',
      report: 'text/plain',
    };
    return types[type] || 'text/plain';
  };

  const getFileExtension = (type: ClaraArtifactType): string => {
    const extensions = {
      code: artifact.language || 'txt',
      html: 'html',
      markdown: 'md',
      csv: 'csv',
      json: 'json',
      table: 'json',
      chart: 'json',
      mermaid: 'mmd',
      diagram: 'txt',
      report: 'txt',
    };
    return extensions[type] || 'txt';
  };

  const renderArtifactContent = () => {
    const onFullScreen = () => setIsModalOpen(true);
    
    switch (artifact.type) {
      case 'code':
        return (
          <CodeHighlighter 
            code={artifact.content} 
            language={artifact.language || 'text'} 
            isDark={isDark}
            showLineNumbers={true}
            onFullScreen={onFullScreen}
          />
        );
      
      case 'table':
      case 'csv':
        return <TableRenderer content={artifact.content} onFullScreen={onFullScreen} />;
      
      case 'html':
        return <HtmlRenderer content={artifact.content} />;
      
      case 'markdown':
        return <MarkdownRenderer content={artifact.content} />;
      
      case 'chart':
        return <InteractiveChartRenderer content={artifact.content} artifact={artifact} onFullScreen={onFullScreen} />;
      
      case 'mermaid':
      case 'diagram':
        return <MermaidRenderer content={artifact.content} />;
      
      case 'json':
        return (
          <CodeHighlighter 
            code={JSON.stringify(JSON.parse(artifact.content), null, 2)} 
            language="json" 
            isDark={isDark}
            showLineNumbers={true}
            onFullScreen={onFullScreen}
          />
        );
      
      // Enhanced artifact types
      case 'react-component' as any:
        return <ReactComponentRenderer content={artifact.content} />;
      
      case 'interactive-chart' as any:
        return <InteractiveChartRenderer content={artifact.content} artifact={artifact} onFullScreen={onFullScreen} />;
      
      default:
        return (
          <pre className="p-4 text-sm whitespace-pre-wrap font-mono bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 overflow-x-auto">
            {artifact.content}
          </pre>
        );
    }
  };

  // Render content for modal (full-screen version)
  const renderModalContent = () => {
    switch (artifact.type) {
      case 'code':
        return (
          <CodeHighlighter 
            code={artifact.content} 
            language={artifact.language || 'text'} 
            isDark={isDark}
            showLineNumbers={true}
            maxHeight={undefined} // No height limit in modal
          />
        );
      
      case 'table':
      case 'csv':
        return <TableRenderer content={artifact.content} />;
      
      case 'html':
        return (
          <div className="h-full">
            <iframe
              srcDoc={artifact.content}
              className="w-full h-full min-h-[600px] bg-white border-0"
              sandbox="allow-scripts allow-same-origin"
              title="HTML Preview - Full Screen"
            />
          </div>
        );
      
      case 'markdown':
        return (
          <div className="prose dark:prose-invert max-w-none h-full overflow-auto">
            <ReactMarkdown>{artifact.content}</ReactMarkdown>
          </div>
        );
      
      case 'chart':
        return (
          <div className="h-full min-h-[600px]">
            <InteractiveChartRenderer content={artifact.content} artifact={artifact} />
          </div>
        );
      
      case 'mermaid':
      case 'diagram':
        return (
          <div className="h-full flex items-center justify-center p-4">
            <div className="w-full h-full max-w-full max-h-full overflow-auto bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <FullScreenMermaidRenderer content={artifact.content} />
            </div>
          </div>
        );
      
      case 'json':
        return (
          <CodeHighlighter 
            code={JSON.stringify(JSON.parse(artifact.content), null, 2)} 
            language="json" 
            isDark={isDark}
            showLineNumbers={true}
            maxHeight={undefined} // No height limit in modal
          />
        );
      
      // Enhanced artifact types
      case 'react-component' as any:
        return <ReactComponentRenderer content={artifact.content} />;
      
      case 'interactive-chart' as any:
        return (
          <div className="h-full min-h-[600px]">
            <InteractiveChartRenderer content={artifact.content} artifact={artifact} />
          </div>
        );
      
      default:
        return (
          <pre className="p-4 text-sm whitespace-pre-wrap font-mono bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-200 overflow-auto h-full">
            {artifact.content}
          </pre>
        );
    }
  };

  return (
    <>
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800">
        {/* Header */}
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <IconComponent className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {artifact.title}
            </span>
            {artifact.language && (
              <span className="px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded">
                {artifact.language}
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-1">
            {/* Full Screen button */}
            <button
              onClick={() => setIsModalOpen(true)}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
              title="View in full screen"
            >
              <Maximize2 className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>
            
            {/* Toggle expanded button */}
            <button
              onClick={handleToggleExpanded}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
              title={expanded ? "Collapse" : "Expand"}
            >
              {expanded ? (
                <EyeOff className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              )}
            </button>
            
            {/* Copy button */}
            <button
              onClick={handleCopy}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
              title="Copy content"
            >
              {copied ? (
                <Check className="w-4 h-4 text-green-500" />
              ) : (
                <Copy className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              )}
            </button>
            
            {/* Download button */}
            <button
              onClick={handleDownload}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
              title="Download"
            >
              <Download className="w-4 h-4 text-gray-500 dark:text-gray-400" />
            </button>
          </div>
        </div>
        
        {/* Content */}
        {expanded && (
          <div className="overflow-hidden">
            {renderArtifactContent()}
          </div>
        )}
      </div>

      {/* Full Screen Modal */}
      <ArtifactModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        artifact={artifact}
      >
        {renderModalContent()}
      </ArtifactModal>
    </>
  );
};

// Add this debug function near the end of the file, before the export
if (process.env.NODE_ENV === 'development') {
  // Debug function to test Mermaid rendering directly
  (window as any).testMermaidRendering = async (diagramCode?: string) => {
    const testCode = diagramCode || `graph TD
    A[User Interface] --> B[API Gateway]
    B --> C[User Service]
    B --> D[Product Service]`;

    console.log('🧪 Testing Mermaid rendering directly...');
    console.log('📝 Test diagram code:', testCode);

    try {
      // Import mermaid
      const mermaid = await import('mermaid');
      console.log('✅ Mermaid imported successfully');

      // Initialize with simple config
      mermaid.default.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose'
      });
      console.log('✅ Mermaid initialized');

      // Try to render
      const uniqueId = `test-diagram-${Date.now()}`;
      const result = await mermaid.default.render(uniqueId, testCode);
      console.log('✅ Mermaid render result:', result);
      console.log('📏 SVG length:', result.svg.length);
      console.log('📋 SVG preview:', result.svg.substring(0, 200) + '...');

      // Create a test container and display the result
      const testContainer = document.createElement('div');
      testContainer.innerHTML = result.svg;
      testContainer.style.position = 'fixed';
      testContainer.style.top = '50px';
      testContainer.style.right = '50px';
      testContainer.style.background = 'white';
      testContainer.style.border = '2px solid red';
      testContainer.style.padding = '20px';
      testContainer.style.zIndex = '9999';
      testContainer.style.maxWidth = '400px';
      testContainer.style.maxHeight = '400px';
      testContainer.style.overflow = 'auto';
      
      // Add close button
      const closeBtn = document.createElement('button');
      closeBtn.textContent = 'Close Test';
      closeBtn.style.position = 'absolute';
      closeBtn.style.top = '5px';
      closeBtn.style.right = '5px';
      closeBtn.style.background = 'red';
      closeBtn.style.color = 'white';
      closeBtn.style.border = 'none';
      closeBtn.style.padding = '5px';
      closeBtn.style.cursor = 'pointer';
      closeBtn.onclick = () => document.body.removeChild(testContainer);
      testContainer.appendChild(closeBtn);
      
      document.body.appendChild(testContainer);
      
      console.log('✅ Test diagram displayed on screen (top-right corner)');
      return result;
      
    } catch (error) {
      console.error('❌ Mermaid test failed:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      return null;
    }
  };

  // Test with the user's specific diagram
  (window as any).testUserDiagram = () => {
    const userDiagram = `graph TD
    A[User Interface] --> A1[Web Application]
    A --> A2[Mobile Application]
    
    A1 --> B[API Gateway]
    A2 --> B
    
    B --> C[User Service]
    B --> E[Product Service]
    B --> G[Order Service]
    B --> I[Payment Service]
    B --> K[Cart Service]
    B --> M[Shipping Service]
    B --> O[Notification Service]
    
    subgraph Microservices
        C -->|Database| D[User Database]
        E -->|Database| F[Product Database]
        G -->|Database| H[Order Database]
        I -->|Third-party API| J[Payment Gateway]
        K -->|Database| L[Cart Database]
        M -->|Database| N[Shipping Database]
        O -->|Database| P[Notification Database]
    end
    
    subgraph Infrastructure
        Q[Container Orchestration]
        R[Load Balancer]
        S[Service Discovery]
        
        Q --> R
        R --> S
    end
    
    Q -.-> C
    Q -.-> E
    Q -.-> G
    Q -.-> I
    Q -.-> K
    Q -.-> M
    Q -.-> O`;

    return (window as any).testMermaidRendering(userDiagram);
  };

  console.log('🧪 Mermaid debug functions available:');
  console.log('- window.testMermaidRendering() - Test basic Mermaid rendering');
  console.log('- window.testUserDiagram() - Test the user\'s specific diagram');
}

export default ClaraArtifactRenderer; 