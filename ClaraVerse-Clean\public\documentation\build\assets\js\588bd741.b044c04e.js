"use strict";(self.webpackChunkdocumentation=self.webpackChunkdocumentation||[]).push([[324],{3014:(e,n,i)=>{i.r(n),i.d(n,{assets:()=>o,contentTitle:()=>a,default:()=>h,frontMatter:()=>t,metadata:()=>s,toc:()=>d});const s=JSON.parse('{"id":"getting-started/quick-start","title":"Quick Start Guide","description":"Get up and running with <PERSON> in minutes","source":"@site/docs/getting-started/quick-start.md","sourceDirName":"getting-started","slug":"/getting-started/quick-start","permalink":"/getting-started/quick-start","draft":false,"unlisted":false,"tags":[],"version":"current","sidebarPosition":3,"frontMatter":{"title":"Quick Start Guide","description":"Get up and running with <PERSON> in minutes","sidebar_position":3},"sidebar":"docsSidebar","previous":{"title":"Installation","permalink":"/getting-started/installation"}}');var r=i(4848),l=i(8453);const t={title:"Quick Start Guide",description:"Get up and running with Clara in minutes",sidebar_position:3},a="\ud83d\ude80 Quick Start Guide",o={},d=[{value:"Prerequisites",id:"prerequisites",level:2},{value:"Installation",id:"installation",level:2},{value:"Option 1: Download Release (Recommended)",id:"option-1-download-release-recommended",level:3},{value:"Option 2: Build from Source",id:"option-2-build-from-source",level:3},{value:"First Launch Setup",id:"first-launch-setup",level:2},{value:"1. Welcome Screen",id:"1-welcome-screen",level:3},{value:"2. AI Provider Configuration",id:"2-ai-provider-configuration",level:3},{value:"OpenAI Setup",id:"openai-setup",level:4},{value:"Anthropic Setup",id:"anthropic-setup",level:4},{value:"Ollama Setup (Local)",id:"ollama-setup-local",level:4},{value:"3. Verify Installation",id:"3-verify-installation",level:3},{value:"Essential First Steps",id:"essential-first-steps",level:2},{value:"\ud83e\udd16 Try the AI Assistant",id:"-try-the-ai-assistant",level:3},{value:"\ud83c\udfa8 Generate Your First Image",id:"-generate-your-first-image",level:3},{value:"\u26a1 Build Your First Agent",id:"-build-your-first-agent",level:3},{value:"Quick Tips for Success",id:"quick-tips-for-success",level:2},{value:"Next Steps",id:"next-steps",level:2},{value:"\ud83d\udd27 <strong>Configuration</strong>",id:"-configuration",level:3},{value:"\ud83d\udcd6 <strong>Learn More</strong>",id:"-learn-more",level:3},{value:"\ud83d\udcda <strong>Installation Details</strong>",id:"-installation-details",level:3},{value:"Getting Help",id:"getting-help",level:2}];function c(e){const n={a:"a",admonition:"admonition",code:"code",h1:"h1",h2:"h2",h3:"h3",h4:"h4",header:"header",hr:"hr",li:"li",ol:"ol",p:"p",pre:"pre",strong:"strong",ul:"ul",...(0,l.R)(),...e.components};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.header,{children:(0,r.jsx)(n.h1,{id:"-quick-start-guide",children:"\ud83d\ude80 Quick Start Guide"})}),"\n",(0,r.jsx)(n.p,{children:"Get Clara up and running in just a few minutes! This guide will walk you through the essentials to start using Clara's powerful AI-powered workspace."}),"\n",(0,r.jsx)(n.h2,{id:"prerequisites",children:"Prerequisites"}),"\n",(0,r.jsx)(n.p,{children:"Before you begin, make sure you have:"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Node.js"})," (version 18 or higher)"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"A modern web browser"})," (Chrome, Firefox, Safari, Edge)"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"An AI provider API key"})," (OpenAI, Anthropic, or local Ollama)"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"installation",children:"Installation"}),"\n",(0,r.jsx)(n.h3,{id:"option-1-download-release-recommended",children:"Option 1: Download Release (Recommended)"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["Visit the ",(0,r.jsx)(n.a,{href:"https://github.com/your-org/clara/releases",children:"Clara Releases"})," page"]}),"\n",(0,r.jsxs)(n.li,{children:["Download the latest version for your platform:","\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Windows"}),": ",(0,r.jsx)(n.code,{children:"Clara-Setup-x.x.x.exe"})]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"macOS"}),": ",(0,r.jsx)(n.code,{children:"Clara-x.x.x.dmg"})]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Linux"}),": ",(0,r.jsx)(n.code,{children:"Clara-x.x.x.AppImage"})]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.li,{children:"Install and launch Clara"}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"option-2-build-from-source",children:"Option 2: Build from Source"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"# Clone the repository\ngit clone https://github.com/your-org/clara.git\ncd clara\n\n# Install dependencies\nnpm install\n\n# Start development server\nnpm run dev\n"})}),"\n",(0,r.jsx)(n.h2,{id:"first-launch-setup",children:"First Launch Setup"}),"\n",(0,r.jsx)(n.p,{children:"When you first open Clara, you'll be guided through a quick onboarding process:"}),"\n",(0,r.jsx)(n.h3,{id:"1-welcome-screen",children:"1. Welcome Screen"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Enter your name for personalization"}),"\n",(0,r.jsx)(n.li,{children:"Choose your preferred theme (Light/Dark)"}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"2-ai-provider-configuration",children:"2. AI Provider Configuration"}),"\n",(0,r.jsx)(n.p,{children:"Choose and configure your AI provider:"}),"\n",(0,r.jsx)(n.h4,{id:"openai-setup",children:"OpenAI Setup"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"# Required: Your OpenAI API key\nAPI_KEY=sk-your-openai-api-key-here\n"})}),"\n",(0,r.jsx)(n.h4,{id:"anthropic-setup",children:"Anthropic Setup"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"# Required: Your Anthropic API key  \nAPI_KEY=sk-ant-your-anthropic-api-key-here\n"})}),"\n",(0,r.jsx)(n.h4,{id:"ollama-setup-local",children:"Ollama Setup (Local)"}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{className:"language-bash",children:"# Install Ollama first\ncurl -fsSL https://ollama.ai/install.sh | sh\n\n# Pull a model\nollama pull llama2\n\n# Clara will auto-detect local Ollama\n"})}),"\n",(0,r.jsx)(n.h3,{id:"3-verify-installation",children:"3. Verify Installation"}),"\n",(0,r.jsx)(n.p,{children:"After setup, test your installation:"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Open Clara Assistant"})," - Click the chat icon in the sidebar"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Send a test message"}),' - Try: "Hello Clara, tell me about your features"']}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Check response"})," - You should get a helpful response about Clara's capabilities"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"essential-first-steps",children:"Essential First Steps"}),"\n",(0,r.jsx)(n.h3,{id:"-try-the-ai-assistant",children:"\ud83e\udd16 Try the AI Assistant"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Access the Assistant"}),":"]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Click the ",(0,r.jsx)(n.strong,{children:"Bot"})," icon in the sidebar"]}),"\n",(0,r.jsxs)(n.li,{children:["Or use the keyboard shortcut ",(0,r.jsx)(n.code,{children:"Ctrl+Shift+A"})," (Windows/Linux) or ",(0,r.jsx)(n.code,{children:"Cmd+Shift+A"})," (macOS)"]}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Start a Conversation"}),":"]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"Hello Clara! What can you help me with today?\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Explore Capabilities"}),":"]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Ask questions about any topic"}),"\n",(0,r.jsx)(n.li,{children:"Request code assistance"}),"\n",(0,r.jsx)(n.li,{children:"Get help with writing and analysis"}),"\n",(0,r.jsx)(n.li,{children:"Use tools and integrations"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"-generate-your-first-image",children:"\ud83c\udfa8 Generate Your First Image"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Open Image Generation"}),":"]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Click ",(0,r.jsx)(n.strong,{children:"Image"})," in the sidebar"]}),"\n",(0,r.jsx)(n.li,{children:"Or navigate to the Image Generation tab"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Create an Image"}),":"]}),"\n",(0,r.jsx)(n.pre,{children:(0,r.jsx)(n.code,{children:"A serene Japanese garden with cherry blossoms in full bloom, \nsoft pink petals falling, peaceful zen atmosphere, \nhigh quality, detailed\n"})}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Customize Settings"}),":"]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Adjust image size and quality"}),"\n",(0,r.jsx)(n.li,{children:"Try different art styles"}),"\n",(0,r.jsx)(n.li,{children:"Experiment with prompts"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h3,{id:"-build-your-first-agent",children:"\u26a1 Build Your First Agent"}),"\n",(0,r.jsxs)(n.ol,{children:["\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Access Agent Studio"}),":"]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:["Click ",(0,r.jsx)(n.strong,{children:"Agents"})," in the sidebar"]}),"\n",(0,r.jsx)(n.li,{children:'Start with the "Create New Agent" button'}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Use the Visual Builder"}),":"]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Drag and drop nodes to create workflows"}),"\n",(0,r.jsx)(n.li,{children:"Connect inputs and outputs"}),"\n",(0,r.jsx)(n.li,{children:"Configure node parameters"}),"\n"]}),"\n"]}),"\n",(0,r.jsxs)(n.li,{children:["\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Test Your Agent"}),":"]}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsx)(n.li,{children:"Use the built-in testing tools"}),"\n",(0,r.jsx)(n.li,{children:"Debug workflows step by step"}),"\n",(0,r.jsx)(n.li,{children:"Export and share your agents"}),"\n"]}),"\n"]}),"\n"]}),"\n",(0,r.jsx)(n.h2,{id:"quick-tips-for-success",children:"Quick Tips for Success"}),"\n",(0,r.jsx)(n.admonition,{title:"Pro Tips",type:"tip",children:(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Explore the Dashboard"}),": Get familiar with the overview and quick actions"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Check Settings"}),": Customize Clara to match your workflow preferences"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Use Keyboard Shortcuts"}),": Speed up your workflow with built-in shortcuts"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Save Frequently"}),": Your work is automatically saved, but manual saves are good practice"]}),"\n"]})}),"\n",(0,r.jsx)(n.admonition,{title:"Common Issues",type:"warning",children:(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"API Keys"}),": Make sure your AI provider API keys are valid and have sufficient credits"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Network"}),": Clara requires internet connectivity for cloud AI providers"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Memory"}),": For local models (Ollama), ensure you have sufficient RAM"]}),"\n"]})}),"\n",(0,r.jsx)(n.h2,{id:"next-steps",children:"Next Steps"}),"\n",(0,r.jsx)(n.p,{children:"Now that Clara is running, explore these features:"}),"\n",(0,r.jsxs)(n.h3,{id:"-configuration",children:["\ud83d\udd27 ",(0,r.jsx)(n.strong,{children:"Configuration"})]}),"\n",(0,r.jsx)(n.p,{children:"Customize Clara's behavior, themes, and integrations through the Settings page"}),"\n",(0,r.jsxs)(n.h3,{id:"-learn-more",children:["\ud83d\udcd6 ",(0,r.jsx)(n.strong,{children:"Learn More"})]}),"\n",(0,r.jsxs)(n.p,{children:["Read the ",(0,r.jsx)(n.a,{href:"/getting-started/introduction",children:"Introduction Guide"})," to understand Clara's full capabilities"]}),"\n",(0,r.jsxs)(n.h3,{id:"-installation-details",children:["\ud83d\udcda ",(0,r.jsx)(n.strong,{children:"Installation Details"})]}),"\n",(0,r.jsxs)(n.p,{children:["Check the complete ",(0,r.jsx)(n.a,{href:"/getting-started/installation",children:"Installation Guide"})," for advanced setup options"]}),"\n",(0,r.jsx)(n.h2,{id:"getting-help",children:"Getting Help"}),"\n",(0,r.jsx)(n.p,{children:"If you run into any issues:"}),"\n",(0,r.jsxs)(n.ul,{children:["\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Documentation"}),": Browse our comprehensive guides"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"GitHub Issues"}),": Report bugs or request features"]}),"\n",(0,r.jsxs)(n.li,{children:[(0,r.jsx)(n.strong,{children:"Community"}),": Join discussions and get help from other users"]}),"\n"]}),"\n",(0,r.jsx)(n.hr,{}),"\n",(0,r.jsxs)(n.p,{children:[(0,r.jsx)(n.strong,{children:"Congratulations!"})," \ud83c\udf89 You're now ready to explore Clara's full potential. The AI-powered workspace is at your fingertips!"]})]})}function h(e={}){const{wrapper:n}={...(0,l.R)(),...e.components};return n?(0,r.jsx)(n,{...e,children:(0,r.jsx)(c,{...e})}):c(e)}},8453:(e,n,i)=>{i.d(n,{R:()=>t,x:()=>a});var s=i(6540);const r={},l=s.createContext(r);function t(e){const n=s.useContext(l);return s.useMemo((function(){return"function"==typeof e?e(n):{...n,...e}}),[n,e])}function a(e){let n;return n=e.disableParentContext?"function"==typeof e.components?e.components(r):e.components||r:t(e.components),s.createElement(l.Provider,{value:n},e.children)}}}]);